{"id": 16, "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "provider": "local", "confirmed": true, "blocked": false, "license": "", "phone": "**********", "bio": "I'm 6.2 ft tall and now I live in Calgary.", "address": "223 14 Street NW ", "firstname": "<PERSON>", "lastname": "<PERSON>", "position": "IT Director", "bioTitle": "Let’s Get to Know Each Other!", "applicationLink": "https://", "brokerage": "Indi Mortgage ", "mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2502.8922914360755!2d-113.97212979999999!3d51.1473373!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x537163cbfe45ad89%3A0xd1ca946f48cc377d!2s4310%20104%20Ave%20NE%20%233242%2C%20Calgary%2C%20AB%20", "facebook": "https://facebook.com/indimortgage/", "instagram": "https://instagram.com/indimortgage/", "twitter": "https://twitter.com/IndiMortgage", "linkedin": "https://ca.linkedin.com/company/indimortgage", "youtube": "https://youtube.com/codesigners", "whatsapp": false, "hasLogo2": false, "fax": null, "hasCustomBanner": false, "titles": "", "photoOnPrintable": true, "website": "https://brunosousa.cc", "ext": null, "qrCodes": [{"id": "63bda0f042ef20001601a940", "url": "brunao.me", "qrImage": "https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/qrcode_brunao_me_f00faedd40.png", "isLastUsed": false}, {"id": "6627ff6ea042e000159aa936", "url": "https://brunosousa.com.br", "qrImage": "https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/brunosousa_com_br_eeeb049378.png", "isLastUsed": true}, {"id": "663ab220b200d300153897e1", "url": "https://docs.google.com/forms/d/e/1FAIpQLSet4htbgypy3AZZtG5TbtXxSiVpBgO02-J9urh4T04m7c4dCw/viewform", "qrImage": "https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/viewform_32801e2c14.png", "isLastUsed": false}, {"id": "66967fc8d04ec0c320546a86", "url": "https://brunosou.com", "qrImage": "https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/brunosou_com_cbc1341f91.png", "isLastUsed": false}, {"id": "66d4e794d054c0c6722741fe", "url": "https://thebrunosou.com", "qrImage": "https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/thebrunosou_com_2c7cffd11c.png", "isLastUsed": false}, {"id": "67ecc1a319fda4dc701fd3e6", "url": "https://brunosousa.cc", "qrImage": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1743569314368_dl8bs-qrcode_https___brunosousa.cc_.png"}], "qrCodeOnPrintable": false, "isOnboarding": false, "homePhone": null, "cellPhone": "**********", "emergencyContact": "<PERSON><PERSON><PERSON>", "emergencyPhone": "3688872122", "birthdate": "2024-10-16", "startDate": "2016-09-05", "dietRestriction": "Fructosis", "additionalNotes": "2 cats: <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "middlename": "", "city": "Calgary", "postalCode": "N2H3X6", "tollfree": null, "workEmail": "<EMAIL>", "websiteOptIn": true, "ownDomain": true, "providedDomain": false, "websiteDomainName": "brunao.me", "websiteDomainRegistrar": "gandi.net", "tollfreeExt": null, "gender": "male", "appointmentScheduleLink": "https://brunosousa.cc", "province": "manitoba", "tshirtSize": "XL", "additionalDomainNames": "brunosousa.com.br\ncodesigners.cc\ndjfsldkjf,\nksjdlfkjlk,\nlksjdf,\njjj\nj", "secondaryWebsite": "https://youtube.com/codesigners", "onboardingStartDate": "2022-09-16", "onboardingEndDate": "2022-10-06", "loginCount": "48", "legalName": "", "preferredName": "", "googleTag": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "facebookPixelTag": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "googleWebsiteVerification": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "googleTagManagerInHead": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "googleTagManagerInBody": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "thirdPartyScriptTag": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "emptyPrintableFooter": false, "googleReviewsLink": "https://", "facebookHandler": null, "instagramHandler": null, "linkedinHandler": null, "twitterHandler": null, "youtubeHandler": null, "notListed": null, "personalAddress": "850 11 St SW", "personalCity": "Calgary", "personalProvince": "Alberta", "personalPostalCode": "T2P 1P6", "personalSuiteUnit": "3303", "suiteUnit": null, "licensed": true, "chatWidgetCode": "Testing New", "reviewWidgetCode": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "emailNotifications": true, "circularPhotoUrl": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1752775980069_2t6w6_<PERSON>_Portrait_1_low_88133367f0-1752775980069_2t6w6-<PERSON>_Portrait-1_low.png", "isStaffMember": true, "tiktok": null, "tiktokHandler": null, "pinterest": null, "pinterestHandler": null, "threads": null, "threadsHandler": null, "bluesky": null, "blueskyHandler": null, "websiteGitBranch": "<PERSON><PERSON><PERSON><PERSON>", "isComplianceStaff": true, "createdAt": "2021-04-20T19:16:53.149Z", "updatedAt": "2025-08-22T16:28:40.152Z", "documentId": "oq8x21rbsfaqaz3m2y822ex0", "publishedAt": "2025-08-20T17:53:29.263Z"}