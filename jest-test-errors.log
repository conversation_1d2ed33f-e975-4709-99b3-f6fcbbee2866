$ jest
 PASS  src/features/printables/hooks/__tests__/use-printables-category.test.ts
 PASS  src/features/printables/hooks/__tests__/use-qr-codes.test.ts
  ● Console

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: Raw user data response: { data: { qrCodes: [ [Object], [Object] ] } }

      at log (src/features/printables/hooks/use-qr-codes.ts:60:15)

    console.log
      useQRCodes: Actual user data: {
        qrCodes: [
          {
            id: 'qr-1',
            url: 'https://example.com',
            qrImage: 'data:image/png;base64,mock-qr-image',
            isLastUsed: true
          },
          {
            id: 'qr-2',
            url: 'https://example2.com',
            qrImage: 'data:image/png;base64,mock-qr-image-2',
            isLastUsed: false
          }
        ]
      }

      at log (src/features/printables/hooks/use-qr-codes.ts:64:15)

    console.log
      useQRCodes: Extracted QR codes: [
        {
          id: 'qr-1',
          url: 'https://example.com',
          qrImage: 'data:image/png;base64,mock-qr-image',
          isLastUsed: true
        },
        {
          id: 'qr-2',
          url: 'https://example2.com',
          qrImage: 'data:image/png;base64,mock-qr-image-2',
          isLastUsed: false
        }
      ]

      at log (src/features/printables/hooks/use-qr-codes.ts:67:15)

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: Raw user data response: {
        qrCodes: [
          {
            id: 'qr-1',
            url: 'https://example.com',
            qrImage: 'data:image/png;base64,mock-qr-image'
          }
        ]
      }

      at log (src/features/printables/hooks/use-qr-codes.ts:60:15)

    console.log
      useQRCodes: Actual user data: {
        qrCodes: [
          {
            id: 'qr-1',
            url: 'https://example.com',
            qrImage: 'data:image/png;base64,mock-qr-image'
          }
        ]
      }

      at log (src/features/printables/hooks/use-qr-codes.ts:64:15)

    console.log
      useQRCodes: Extracted QR codes: [
        {
          id: 'qr-1',
          url: 'https://example.com',
          qrImage: 'data:image/png;base64,mock-qr-image'
        }
      ]

      at log (src/features/printables/hooks/use-qr-codes.ts:67:15)

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: Raw user data response: {}

      at log (src/features/printables/hooks/use-qr-codes.ts:60:15)

    console.log
      useQRCodes: Actual user data: {}

      at log (src/features/printables/hooks/use-qr-codes.ts:64:15)

    console.log
      useQRCodes: Extracted QR codes: []

      at log (src/features/printables/hooks/use-qr-codes.ts:67:15)

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: useEffect triggered { userId: undefined, hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Missing user info or token { userId: undefined, hasToken: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:32:15)

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: false, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Missing user info or token { userId: 'user-1', hasToken: false }

      at log (src/features/printables/hooks/use-qr-codes.ts:32:15)

    console.log
      useQRCodes: useEffect triggered { userId: 'user-1', hasToken: true, initialized: true }

      at log (src/features/printables/hooks/use-qr-codes.ts:84:13)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: Raw user data response: { qrCodes: [] }

      at log (src/features/printables/hooks/use-qr-codes.ts:60:15)

    console.log
      useQRCodes: Actual user data: { qrCodes: [] }

      at log (src/features/printables/hooks/use-qr-codes.ts:64:15)

    console.log
      useQRCodes: Extracted QR codes: []

      at log (src/features/printables/hooks/use-qr-codes.ts:67:15)

    console.log
      useQRCodes: Fetching QR codes for user: user-1

      at log (src/features/printables/hooks/use-qr-codes.ts:44:15)

    console.log
      useQRCodes: Raw user data response: { qrCodes: [] }

      at log (src/features/printables/hooks/use-qr-codes.ts:60:15)

    console.log
      useQRCodes: Actual user data: { qrCodes: [] }

      at log (src/features/printables/hooks/use-qr-codes.ts:64:15)

    console.log
      useQRCodes: Extracted QR codes: []

      at log (src/features/printables/hooks/use-qr-codes.ts:67:15)

 PASS  src/features/printables/hooks/__tests__/use-printable-document.test.ts
 PASS  src/app/(auth)/forgot-password/__tests__/page.test.tsx
 PASS  src/shared/contexts/__tests__/auth-context.test.tsx
  ● Console

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: false

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      No valid authentication found, setting to unauthenticated state

      at log (src/shared/contexts/auth-context.tsx:70:17)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: [ { id: '1', Title: 'Partners', slug: 'partners' } ]

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 1

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: invalid

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: string

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 7

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: false

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      No valid authentication found, setting to unauthenticated state

      at log (src/shared/contexts/auth-context.tsx:70:17)

    console.log
      Attempting login to: http://localhost:1339/api/auth/local

      at log (src/shared/contexts/auth-context.tsx:197:17)

    console.log
      Login response user data: {
        id: '1',
        email: '<EMAIL>',
        firstname: 'John',
        lastname: 'Doe',
        role: { name: 'admin' }
      }

      at log (src/shared/contexts/auth-context.tsx:221:17)

    console.log
      Normalized user data: {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        avatar: undefined,
        company: undefined,
        documentId: undefined
      }

      at log (src/shared/contexts/auth-context.tsx:239:17)

    console.log
      Login successful, fetching complete user data...

      at log (src/shared/contexts/auth-context.tsx:255:19)

    console.log
      Login successful for user: <EMAIL>

      at log (src/shared/contexts/auth-context.tsx:289:17)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: false

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      No valid authentication found, setting to unauthenticated state

      at log (src/shared/contexts/auth-context.tsx:70:17)

    console.log
      Attempting login to: http://localhost:1339/api/auth/local

      at log (src/shared/contexts/auth-context.tsx:197:17)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: true

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      Fetching user data...

      at log (src/shared/contexts/auth-context.tsx:87:17)

    console.log
      Auth context - Raw onePages data: []

      at log (src/shared/contexts/auth-context.tsx:90:17)

    console.log
      Auth context - onePages data type: object

      at log (src/shared/contexts/auth-context.tsx:91:17)

    console.log
      Auth context - onePages length: 0

      at log (src/shared/contexts/auth-context.tsx:92:17)

    console.log
      Auth context - Setting authenticated state with data

      at log (src/shared/contexts/auth-context.tsx:95:19)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: false

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      No valid authentication found, setting to unauthenticated state

      at log (src/shared/contexts/auth-context.tsx:70:17)

    console.log
      Attempting login to: http://localhost:1339/api/auth/local

      at log (src/shared/contexts/auth-context.tsx:197:17)

    console.log
      Login response user data: {
        id: '607f28a5cd063c35088bf735',
        email: '<EMAIL>',
        firstname: 'Bruno',
        lastname: 'de Sousa',
        role: { name: 'admin', id: 'role-1' }
      }

      at log (src/shared/contexts/auth-context.tsx:221:17)

    console.log
      Normalized user data: {
        id: '607f28a5cd063c35088bf735',
        name: 'Bruno de Sousa',
        email: '<EMAIL>',
        role: 'admin',
        avatar: undefined,
        company: undefined,
        documentId: undefined
      }

      at log (src/shared/contexts/auth-context.tsx:239:17)

    console.log
      Login successful, fetching complete user data...

      at log (src/shared/contexts/auth-context.tsx:255:19)

    console.log
      Login successful for user: <EMAIL>

      at log (src/shared/contexts/auth-context.tsx:289:17)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Authentication status: false

      at log (src/shared/contexts/auth-context.tsx:67:15)

    console.log
      No valid authentication found, setting to unauthenticated state

      at log (src/shared/contexts/auth-context.tsx:70:17)

    console.log
      Attempting login to: http://localhost:1339/api/auth/local

      at log (src/shared/contexts/auth-context.tsx:197:17)

    console.log
      Login response user data: {
        id: '1',
        email: '<EMAIL>',
        firstname: 'John',
        lastname: 'Doe',
        role: { name: 'admin' }
      }

      at log (src/shared/contexts/auth-context.tsx:221:17)

    console.log
      Normalized user data: {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        avatar: undefined,
        company: undefined,
        documentId: undefined
      }

      at log (src/shared/contexts/auth-context.tsx:239:17)

    console.log
      Login successful, fetching complete user data...

      at log (src/shared/contexts/auth-context.tsx:255:19)

    console.log
      Login successful for user: <EMAIL>

      at log (src/shared/contexts/auth-context.tsx:289:17)

 PASS  src/features/printables/hooks/__tests__/use-printables.test.ts
 FAIL  src/features/social-media/__tests__/use-social-media.test.ts
  ● Console

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      User province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:76:20)

    console.log
      User object: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:77:20)

    console.log
      User team: { province: 'ontario' }

      at log (src/features/social-media/hooks/use-social-media.ts:78:20)

    console.log
      User province direct: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:79:20)

    console.log
      User team province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:80:20)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 2 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 2 Province data: [ { all: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 2: { all: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 has 'all' flag set to true

      at log (src/features/social-media/lib/utils.ts:24:15)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 3 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 3 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 3: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Available posts after province filtering: 3

      at log (src/features/social-media/hooks/use-social-media.ts:92:20)

    console.log
      Filtered posts: { monthly: 2, extra: 1 }

      at log (src/features/social-media/hooks/use-social-media.ts:97:20)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      User province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:76:20)

    console.log
      User object: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:77:20)

    console.log
      User team: { province: 'ontario' }

      at log (src/features/social-media/hooks/use-social-media.ts:78:20)

    console.log
      User province direct: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:79:20)

    console.log
      User team province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:80:20)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 2 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 2 Province data: [ { all: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 2: { all: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 has 'all' flag set to true

      at log (src/features/social-media/lib/utils.ts:24:15)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 3 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 3 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 3: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Available posts after province filtering: 3

      at log (src/features/social-media/hooks/use-social-media.ts:92:20)

    console.log
      Filtered posts: { monthly: 2, extra: 1 }

      at log (src/features/social-media/hooks/use-social-media.ts:97:20)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      User province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:76:20)

    console.log
      User object: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:77:20)

    console.log
      User team: { province: 'ontario' }

      at log (src/features/social-media/hooks/use-social-media.ts:78:20)

    console.log
      User province direct: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:79:20)

    console.log
      User team province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:80:20)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 2 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 2 Province data: [ { all: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 2: { all: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 has 'all' flag set to true

      at log (src/features/social-media/lib/utils.ts:24:15)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 3 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 3 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 3: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Available posts after province filtering: 3

      at log (src/features/social-media/hooks/use-social-media.ts:92:20)

    console.log
      Filtered posts: { monthly: 2, extra: 1 }

      at log (src/features/social-media/hooks/use-social-media.ts:97:20)

    console.log
      useSocialMedia hook - user: { team: {} }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: {}

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: {}

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      User province: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:76:20)

    console.log
      User object: {}

      at log (src/features/social-media/hooks/use-social-media.ts:77:20)

    console.log
      User team: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:78:20)

    console.log
      User province direct: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:79:20)

    console.log
      User team province: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:80:20)

    console.log
      No user province found, showing all posts as fallback

      at log (src/features/social-media/hooks/use-social-media.ts:85:22)

    console.log
      Available posts after province filtering: 3

      at log (src/features/social-media/hooks/use-social-media.ts:92:20)

    console.log
      Filtered posts: { monthly: 2, extra: 1 }

      at log (src/features/social-media/hooks/use-social-media.ts:97:20)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      User province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:76:20)

    console.log
      User object: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:77:20)

    console.log
      User team: { province: 'ontario' }

      at log (src/features/social-media/hooks/use-social-media.ts:78:20)

    console.log
      User province direct: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:79:20)

    console.log
      User team province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:80:20)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 2 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 2 Province data: [ { all: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 2: { all: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 has 'all' flag set to true

      at log (src/features/social-media/lib/utils.ts:24:15)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 2 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Checking post 3 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)
          at Array.filter (<anonymous>)

    console.log
      Post 3 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)
          at Array.filter (<anonymous>)

    console.log
      Province object for post 3: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)
          at Array.filter (<anonymous>)

    console.log
      Post 3 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)
          at Array.filter (<anonymous>)

    console.log
      Available posts after province filtering: 3

      at log (src/features/social-media/hooks/use-social-media.ts:92:20)

    console.log
      Filtered posts: { monthly: 2, extra: 1 }

      at log (src/features/social-media/hooks/use-social-media.ts:97:20)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      useSocialMedia hook - user: null

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 2,
            documentId: 'doc2',
            month: '2025-06-01',
            isExtra: false,
            Province: [Array]
          },
          {
            id: 3,
            documentId: 'doc3',
            month: null,
            isExtra: true,
            extraTitle: 'Spring Forward Reminder(s)',
            Province: [Array]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": [
              {
                "ontario": true
              }
            ]
          },
          {
            "id": 2,
            "documentId": "doc2",
            "month": "2025-06-01",
            "isExtra": false,
            "Province": [
              {
                "all": true
              }
            ]
          },
          {
            "id": 3,
            "documentId": "doc3",
            "month": null,
            "isExtra": true,
            "extraTitle": "Spring Forward Reminder(s)",
            "Province": [
              {
                "ontario": true
              }
            ]
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 2,
          documentId: 'doc2',
          month: '2025-06-01',
          isExtra: false,
          Province: [ [Object] ]
        },
        {
          id: 3,
          documentId: 'doc3',
          month: null,
          isExtra: true,
          extraTitle: 'Spring Forward Reminder(s)',
          Province: [ [Object] ]
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 3

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: [ { ontario: true } ]

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: { data: [] }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": []
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: []

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 0

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      No posts to process

      at log (src/features/social-media/hooks/use-social-media.ts:71:22)

    console.log
      User loaded, fetching posts...

      at log (src/features/social-media/hooks/use-social-media.ts:59:15)

    console.log
      Fetching social media posts...

      at log (src/features/social-media/hooks/use-social-media.ts:30:15)

    console.log
      API Response: {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: []
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:32:15)

    console.log
      Raw response data: {
        "data": [
          {
            "id": 1,
            "documentId": "doc1",
            "month": "2025-07-01",
            "isExtra": false,
            "Province": []
          }
        ]
      }

      at log (src/features/social-media/hooks/use-social-media.ts:33:15)

    console.log
      Processed posts: [
        {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: []
        }
      ]

      at log (src/features/social-media/hooks/use-social-media.ts:36:15)

    console.log
      First post sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: []
      }

      at log (src/features/social-media/hooks/use-social-media.ts:37:15)

    console.log
      Posts length: 1

      at log (src/features/social-media/hooks/use-social-media.ts:38:15)

    console.log
      First post Province structure: []

      at log (src/features/social-media/hooks/use-social-media.ts:42:17)

    console.log
      First post Province type: object

      at log (src/features/social-media/hooks/use-social-media.ts:43:17)

    console.log
      First post Province is array: true

      at log (src/features/social-media/hooks/use-social-media.ts:44:17)

    console.log
      useSocialMedia hook - user: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:22:11)

    console.log
      useSocialMedia hook - authLoading: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:23:11)

    console.log
      User province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:76:20)

    console.log
      User object: { team: { province: 'ontario' } }

      at log (src/features/social-media/hooks/use-social-media.ts:77:20)

    console.log
      User team: { province: 'ontario' }

      at log (src/features/social-media/hooks/use-social-media.ts:78:20)

    console.log
      User province direct: undefined

      at log (src/features/social-media/hooks/use-social-media.ts:79:20)

    console.log
      User team province: ontario

      at log (src/features/social-media/hooks/use-social-media.ts:80:20)

    console.log
      Post has no Province data: 1

      at log (src/features/social-media/lib/utils.ts:8:13)
          at Array.filter (<anonymous>)

    console.log
      Available posts after province filtering: 0

      at log (src/features/social-media/hooks/use-social-media.ts:92:20)

    console.log
      Filtered posts: { monthly: 0, extra: 0 }

      at log (src/features/social-media/hooks/use-social-media.ts:97:20)

  ● useSocialMedia › fetches posts on mount when user is authenticated

    expect(received).toHaveLength(expected)

    Expected length: 2
    Received length: 0
    Received array:  []

      64 |     });
      65 |
    > 66 |     expect(result.current.monthlyPosts).toHaveLength(2);
         |                                         ^
      67 |     expect(result.current.extraPosts).toHaveLength(1);
      68 |     expect(result.current.isLoading).toBe(false);
      69 |     expect(result.current.error).toBeNull();

      at Object.toHaveLength (src/features/social-media/__tests__/use-social-media.test.ts:66:41)

  ● useSocialMedia › handles user without province gracefully

    expect(received).toHaveLength(expected)

    Expected length: 2
    Received length: 0
    Received array:  []

      158 |
      159 |     // Should still fetch posts even without province
    > 160 |     expect(result.current.monthlyPosts).toHaveLength(2);
          |                                         ^
      161 |     expect(result.current.extraPosts).toHaveLength(1);
      162 |   });
      163 |

      at Object.toHaveLength (src/features/social-media/__tests__/use-social-media.test.ts:160:41)

  ● useSocialMedia › handles user without team gracefully

    expect(received).toHaveLength(expected)

    Expected length: 2
    Received length: 0
    Received array:  []

      179 |
      180 |     // Should still fetch posts even without team
    > 181 |     expect(result.current.monthlyPosts).toHaveLength(2);
          |                                         ^
      182 |     expect(result.current.extraPosts).toHaveLength(1);
      183 |   });
      184 |

      at Object.toHaveLength (src/features/social-media/__tests__/use-social-media.test.ts:181:41)

  ● useSocialMedia › does not fetch posts when user is not authenticated

    expect(jest.fn()).not.toHaveBeenCalled()

    Expected number of calls: 0
    Received number of calls: 1

    1: called with 0 arguments

      210 |     renderHook(() => useSocialMedia());
      211 |
    > 212 |     expect(mockGetLightweightPosts).not.toHaveBeenCalled();
          |                                         ^
      213 |   });
      214 |
      215 |   it('handles empty posts response', async () => {

      at Object.toHaveBeenCalled (src/features/social-media/__tests__/use-social-media.test.ts:212:41)

 PASS  src/features/aod/__tests__/use-aod.test.ts
 PASS  src/shared/hooks/__tests__/use-auth.test.ts
  ● Console

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      login response {
        jwt: 'mock-jwt-token',
        cookie_token: 'mock-cookie-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          firstname: 'John',
          lastname: 'Doe'
        }
      }

      at Object.log [as login] (src/shared/hooks/use-auth.ts:54:17)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      userData From USE AUTH {
        id: '123',
        email: '<EMAIL>',
        firstname: 'John',
        lastname: 'Doe'
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:144:15)

    console.log
      normalizedUserInfo {
        id: '123',
        documentId: '',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        avatar: undefined,
        company: undefined,
        firstname: 'John',
        lastname: 'Doe',
        photo: undefined,
        team: undefined,
        position: undefined,
        workEmail: undefined,
        workPhone: undefined,
        phone: undefined,
        cellPhone: undefined,
        website: undefined,
        brokerage: undefined,
        bio: undefined,
        isOnboarding: undefined,
        isStaffMember: undefined,
        notListed: undefined,
        profileComplete: undefined,
        websiteOptIn: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        province: undefined
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:189:15)

    console.log
      Normalized API user data: {
        id: '123',
        documentId: '',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        avatar: undefined,
        company: undefined,
        firstname: 'John',
        lastname: 'Doe',
        photo: undefined,
        team: undefined,
        position: undefined,
        workEmail: undefined,
        workPhone: undefined,
        phone: undefined,
        cellPhone: undefined,
        website: undefined,
        brokerage: undefined,
        bio: undefined,
        isOnboarding: undefined,
        isStaffMember: undefined,
        notListed: undefined,
        profileComplete: undefined,
        websiteOptIn: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        province: undefined
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:217:15)

    console.log
      Normalized onePages: []

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:218:15)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      userData From USE AUTH {
        _id: { '$oid': '507f1f77bcf86cd799439011' },
        id: '123',
        email: '<EMAIL>',
        firstname: 'John',
        lastname: 'Doe',
        role: '615729ce1026f27e0f34ea5f',
        photo: { url: '/uploads/avatar.jpg' },
        team: { name: 'Development Team' }
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:144:15)

    console.log
      normalizedUserInfo {
        id: '123',
        documentId: '',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        avatar: '/uploads/avatar.jpg',
        company: 'Development Team',
        firstname: 'John',
        lastname: 'Doe',
        photo: { url: '/uploads/avatar.jpg' },
        team: { name: 'Development Team' },
        position: undefined,
        workEmail: undefined,
        workPhone: undefined,
        phone: undefined,
        cellPhone: undefined,
        website: undefined,
        brokerage: undefined,
        bio: undefined,
        isOnboarding: undefined,
        isStaffMember: undefined,
        notListed: undefined,
        profileComplete: undefined,
        websiteOptIn: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        province: undefined
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:189:15)

    console.log
      Normalized API user data: {
        id: '123',
        documentId: '',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        avatar: '/uploads/avatar.jpg',
        company: 'Development Team',
        firstname: 'John',
        lastname: 'Doe',
        photo: { url: '/uploads/avatar.jpg' },
        team: { name: 'Development Team' },
        position: undefined,
        workEmail: undefined,
        workPhone: undefined,
        phone: undefined,
        cellPhone: undefined,
        website: undefined,
        brokerage: undefined,
        bio: undefined,
        isOnboarding: undefined,
        isStaffMember: undefined,
        notListed: undefined,
        profileComplete: undefined,
        websiteOptIn: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        province: undefined
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:217:15)

    console.log
      Normalized onePages: [ { id: '1', _id: undefined, Title: 'Test Page', slug: 'test-page' } ]

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:218:15)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

    console.log
      userData From USE AUTH { id: '123', email: '<EMAIL>' }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:144:15)

    console.log
      normalizedUserInfo {
        id: '123',
        documentId: '',
        name: '<EMAIL>',
        email: '<EMAIL>',
        role: 'user',
        avatar: undefined,
        company: undefined,
        firstname: undefined,
        lastname: undefined,
        photo: undefined,
        team: undefined,
        position: undefined,
        workEmail: undefined,
        workPhone: undefined,
        phone: undefined,
        cellPhone: undefined,
        website: undefined,
        brokerage: undefined,
        bio: undefined,
        isOnboarding: undefined,
        isStaffMember: undefined,
        notListed: undefined,
        profileComplete: undefined,
        websiteOptIn: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        province: undefined
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:189:15)

    console.log
      Normalized API user data: {
        id: '123',
        documentId: '',
        name: '<EMAIL>',
        email: '<EMAIL>',
        role: 'user',
        avatar: undefined,
        company: undefined,
        firstname: undefined,
        lastname: undefined,
        photo: undefined,
        team: undefined,
        position: undefined,
        workEmail: undefined,
        workPhone: undefined,
        phone: undefined,
        cellPhone: undefined,
        website: undefined,
        brokerage: undefined,
        bio: undefined,
        isOnboarding: undefined,
        isStaffMember: undefined,
        notListed: undefined,
        profileComplete: undefined,
        websiteOptIn: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        province: undefined
      }

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:217:15)

    console.log
      Normalized onePages: [
        { id: '1', _id: undefined, Title: 'Valid Page', slug: 'valid-page' },
        {
          id: '3',
          _id: undefined,
          Title: 'Another Valid',
          slug: 'another-valid'
        }
      ]

      at Object.log [as fetchUserData] (src/shared/hooks/use-auth.ts:218:15)

    console.log
      Auth initialization started

      at log (src/shared/contexts/auth-context.tsx:56:15)

 PASS  src/app/(auth)/reset-password/__tests__/page.test.tsx
 PASS  src/features/indi-app/__tests__/use-indi-app.test.ts
  ● Console

    console.log
      Indi App API Data: []

      at log (src/features/indi-app/hooks/use-indi-app.ts:39:17)

    console.log
      Indi App API Data: []

      at log (src/features/indi-app/hooks/use-indi-app.ts:39:17)

    console.log
      Indi App API Data: []

      at log (src/features/indi-app/hooks/use-indi-app.ts:39:17)

 PASS  src/features/social-media/__tests__/social-media-page.test.tsx
  ● Console

    console.log
      SocialMediaPage - monthlyPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:20:11)

    console.log
      SocialMediaPage - extraPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:21:11)

    console.log
      SocialMediaPage - isLoading: false

      at log (src/features/social-media/components/social-media-page.tsx:22:11)

    console.log
      SocialMediaPage - error: null

      at log (src/features/social-media/components/social-media-page.tsx:23:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 0,
        extraPosts: 0,
        showExtras: false,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      No monthly posts, showing skeleton

      at log (src/features/social-media/components/social-media-list.tsx:29:13)

    console.log
      SocialMediaPage - monthlyPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:20:11)

    console.log
      SocialMediaPage - extraPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:21:11)

    console.log
      SocialMediaPage - isLoading: true

      at log (src/features/social-media/components/social-media-page.tsx:22:11)

    console.log
      SocialMediaPage - error: null

      at log (src/features/social-media/components/social-media-page.tsx:23:11)

    console.log
      SocialMediaPage - monthlyPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:20:11)

    console.log
      SocialMediaPage - extraPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:21:11)

    console.log
      SocialMediaPage - isLoading: false

      at log (src/features/social-media/components/social-media-page.tsx:22:11)

    console.log
      SocialMediaPage - error: Failed to fetch posts

      at log (src/features/social-media/components/social-media-page.tsx:23:11)

    console.log
      SocialMediaPage - monthlyPosts: 1

      at log (src/features/social-media/components/social-media-page.tsx:20:11)

    console.log
      SocialMediaPage - extraPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:21:11)

    console.log
      SocialMediaPage - isLoading: false

      at log (src/features/social-media/components/social-media-page.tsx:22:11)

    console.log
      SocialMediaPage - error: null

      at log (src/features/social-media/components/social-media-page.tsx:23:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 1,
        extraPosts: 0,
        showExtras: false,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 0, isExtra: true, showExtras: false }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: undefined

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaPage - monthlyPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:20:11)

    console.log
      SocialMediaPage - extraPosts: 0

      at log (src/features/social-media/components/social-media-page.tsx:21:11)

    console.log
      SocialMediaPage - isLoading: false

      at log (src/features/social-media/components/social-media-page.tsx:22:11)

    console.log
      SocialMediaPage - error: Failed to fetch posts

      at log (src/features/social-media/components/social-media-page.tsx:23:11)

 PASS  src/shared/hooks/__tests__/use-password-reset.test.ts
 PASS  src/shared/lib/__tests__/navigation.test.ts
 PASS  src/features/social-media/__tests__/social-posts-list.test.tsx
  ● Console

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: true
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: true
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.pdf',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.pdf',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.pdf',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.pdf',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.png',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.png',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.txt',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: 'https://example.com/calendar.txt',
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: null,
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration'
          },
          {
            id: 'cap2',
            title: 'B2 Did You Know?',
            postDate: '2025-08-06',
            text: 'Did you know we have ancient civilizations to thank for property taxes?',
            backgroundInfo: 'Property tax history'
          }
        ],
        calendar: null,
        month: null,
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: true

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Captions length: 2

      at log (src/features/social-media/components/social-posts-list.tsx:54:13)

    console.log
      First caption: {
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday to our incredible community across Canada!',
        backgroundInfo: 'Civic Holiday celebration'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:55:13)

    console.log
      First caption type: object

      at log (src/features/social-media/components/social-posts-list.tsx:56:13)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: null,
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: false

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

    console.log
      SocialPostsList props: {
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          },
          {
            id: 'img2',
            name: 'B2-image.jpg',
            url: 'https://example.com/image2.jpg',
            alternativeText: 'Test image 2',
            hash: 'hash2',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 2000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01'
          }
        ],
        captions: null,
        calendar: null,
        month: '2025-07-01',
        isExtra: false
      }

      at log (src/features/social-media/components/social-posts-list.tsx:50:11)

    console.log
      Captions type: object

      at log (src/features/social-media/components/social-posts-list.tsx:51:11)

    console.log
      Captions is array: false

      at log (src/features/social-media/components/social-posts-list.tsx:52:11)

    console.log
      Images sample: {
        id: 'img1',
        name: 'A1-image.jpg',
        url: 'https://example.com/image1.jpg',
        alternativeText: 'Test image 1',
        hash: 'hash1',
        ext: 'jpg',
        mime: 'image/jpeg',
        size: 1000,
        provider: 'local',
        createdAt: '2025-01-01',
        updatedAt: '2025-01-01'
      }

      at log (src/features/social-media/components/social-posts-list.tsx:58:11)

    console.log
      First image caption: undefined

      at log (src/features/social-media/components/social-posts-list.tsx:59:11)

    console.log
      First image alternativeText: Test image 1

      at log (src/features/social-media/components/social-posts-list.tsx:60:11)

 PASS  src/features/newsletter-archive/__tests__/newsletter-archive-page.test.tsx
 PASS  src/features/social-media/__tests__/year-group-selector.test.tsx
 PASS  src/features/printables/lib/__tests__/pdf-utils.test.ts
 PASS  src/features/listing-sheet/lib/__tests__/mortgage-calculations.test.ts
 PASS  src/features/social-media/__tests__/social-media-list.test.tsx
  ● Console

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: true
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 0,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      No monthly posts, showing skeleton

      at log (src/features/social-media/components/social-media-list.tsx:29:13)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 0,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 0, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: undefined

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 0,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      No monthly posts, showing skeleton

      at log (src/features/social-media/components/social-media-list.tsx:29:13)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: false
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      SocialMediaList props: {
        monthlyPosts: 7,
        extraPosts: 2,
        showExtras: true,
        isMonthsMobOpen: true
      }

      at log (src/features/social-media/components/social-media-list.tsx:21:11)

    console.log
      MonthSelector props: { posts: 2, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 8,
        documentId: 'doc8',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

 PASS  src/features/example-feature/__tests__/example-component.test.tsx
 PASS  src/features/printables/components/__tests__/printables-landing-page.test.tsx
 PASS  src/shared/lib/__tests__/file-utils.test.ts
 PASS  src/features/listing-sheet/__tests__/use-listing-sheet.test.ts
 PASS  src/features/listing-sheet/hooks/__tests__/use-listing-sheets.test.ts
 PASS  src/features/notifications/__tests__/notifications-list-page.test.tsx
 PASS  src/features/aod/__tests__/aod-carousel.test.tsx
 PASS  src/features/email-signature/__tests__/social-icons.test.tsx
 PASS  src/features/aod/__tests__/aod-page.test.tsx
 PASS  src/features/social-media/__tests__/month-selector.test.tsx
  ● Console

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: false, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 0, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: undefined

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

    console.log
      MonthSelector props: { posts: 3, isExtra: true, showExtras: true }

      at log (src/features/social-media/components/month-selector.tsx:16:11)

    console.log
      MonthSelector posts sample: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        extraTitle: 'Spring Forward Reminder(s)',
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/components/month-selector.tsx:17:11)

 PASS  src/features/indi-app/__tests__/indi-app-page.test.tsx
 PASS  src/features/logos-fonts/__tests__/use-logos-fonts.test.ts
 PASS  src/features/email-signature/__tests__/signature-context.test.tsx
 PASS  src/features/printables/hooks/__tests__/use-printable-preferences.test.ts
  ● Console

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: null,
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: null,
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

    console.log
      usePrintablePreferences: Current preferences and user data: {
        preferences: {
          photoOnPrintable: false,
          qrCodeOnPrintable: false,
          emptyPrintableFooter: true
        },
        userInfo: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user'
        },
        hasQrCodes: false,
        qrCodesLength: 0
      }

      at log (src/features/printables/hooks/use-printable-preferences.ts:40:11)

 PASS  src/features/brand-materials/__tests__/use-brand-materials.test.ts
 PASS  src/features/social-media/__tests__/api-client.test.ts
 PASS  src/features/compliance/__tests__/use-compliance.test.ts
 PASS  src/features/tutorials/__tests__/use-tutorials.test.ts
 PASS  src/features/printables/__tests__/use-printables.test.ts
 PASS  src/features/email-signature/__tests__/signature-base.test.tsx
 PASS  src/features/awards/__tests__/awards-page.test.tsx
 PASS  src/features/newsletter-archive/__tests__/use-newsletters.test.ts
 PASS  src/features/resources/__tests__/use-resources.test.ts
 PASS  src/features/company-directory/__tests__/use-company-directory.test.ts
 PASS  src/features/calendar/__tests__/use-calendar.test.ts
 PASS  src/features/custom-shop/__tests__/use-custom-shop.test.ts
 PASS  src/features/notifications/__tests__/use-notifications.test.ts
 PASS  src/features/awards/__tests__/use-awards.test.ts
 PASS  src/features/fintrac/__tests__/use-fintrac.test.ts
 PASS  src/features/printables/lib/__tests__/api-client.test.ts
 PASS  src/features/listing-sheet/lib/__tests__/validation-schemas.test.ts
 PASS  src/features/indi-fit-club/__tests__/use-indi-fit-club.test.ts
  ● Console

    console.log
      useIndiFitClub hook is being called!

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:29:11)

    console.log
      Hook returning - data: null isLoading: true error: null

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:100:11)

    console.log
      useEffect triggered, calling fetchIndiFitClubData

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:96:13)

    console.log
      Starting fetch...

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:37:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:87:15)

    console.log
      useIndiFitClub hook is being called!

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:29:11)

    console.log
      Hook returning - data: null isLoading: true error: null

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:100:11)

    console.log
      useEffect triggered, calling fetchIndiFitClubData

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:96:13)

    console.log
      Starting fetch...

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:37:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:87:15)

    console.log
      useIndiFitClub hook is being called!

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:29:11)

    console.log
      Hook returning - data: null isLoading: true error: null

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:100:11)

    console.log
      useEffect triggered, calling fetchIndiFitClubData

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:96:13)

    console.log
      Starting fetch...

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:37:15)

    console.log
      Starting fetch...

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:37:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:87:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-fit-club/hooks/use-indi-fit-club.ts:87:15)

 PASS  src/features/listing-sheet/lib/__tests__/api-client.test.ts
  ● Console

    console.log
      Update URL: /listing-sheets/1

      at Function.log [as updateListingSheet] (src/features/listing-sheet/lib/api-client.ts:211:15)

    console.log
      Update payload: {
        user: 'user-1',
        sheet: { askingPrice: 600000, years: 30, frequency: 'biweekly' },
        title: 'Updated Listing',
        slug: 'updated-listing'
      }

      at Function.log [as updateListingSheet] (src/features/listing-sheet/lib/api-client.ts:212:15)

 PASS  src/features/indi-cares/__tests__/use-indi-cares.test.ts
  ● Console

    console.log
      useIndiCares hook is being called!

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:29:11)

    console.log
      Hook returning - data: null isLoading: true error: null

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:100:11)

    console.log
      useEffect triggered, calling fetchIndiCaresData

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:96:13)

    console.log
      Starting fetch...

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:37:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:87:15)

    console.log
      useIndiCares hook is being called!

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:29:11)

    console.log
      Hook returning - data: null isLoading: true error: null

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:100:11)

    console.log
      useEffect triggered, calling fetchIndiCaresData

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:96:13)

    console.log
      Starting fetch...

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:37:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:87:15)

    console.log
      useIndiCares hook is being called!

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:29:11)

    console.log
      Hook returning - data: null isLoading: true error: null

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:100:11)

    console.log
      useEffect triggered, calling fetchIndiCaresData

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:96:13)

    console.log
      Starting fetch...

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:37:15)

    console.log
      Starting fetch...

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:37:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:87:15)

    console.log
      API Response received: undefined

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:43:15)

    console.log
      Loading finished

      at log (src/features/indi-cares/hooks/use-indi-cares.ts:87:15)

 PASS  src/features/listing-sheet/lib/__tests__/payment-frequency-utils.test.ts
 PASS  src/features/social-media/__tests__/utils.test.ts
  ● Console

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      Validating post: {
        id: undefined,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: undefined value: undefined

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: false

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      Validating post: {
        id: 'invalid',
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: string value: invalid

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: false

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      Validating post: {
        id: 1,
        documentId: undefined,
        month: '2025-07-01',
        isExtra: false,
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: undefined value: undefined

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: false

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      Validating post: {
        id: 1,
        documentId: 123,
        month: '2025-07-01',
        isExtra: false,
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: number value: 123

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: false

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: null,
        isExtra: true,
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: object value: null

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: true

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: null,
        isExtra: false,
        Province: [ { all: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: object value: null

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: false

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      Checking post undefined for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post undefined Province data: [ { all: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post undefined: { all: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Post undefined has 'all' flag set to true

      at log (src/features/social-media/lib/utils.ts:24:15)
          at Array.some (<anonymous>)

    console.log
      Post undefined available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Checking post undefined for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post undefined Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post undefined: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post undefined matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post undefined available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Checking post undefined for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post undefined Province data: [ { quebec: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post undefined: { quebec: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "quebec" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post undefined available: false

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Checking post undefined for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post undefined Province data: [ { ontario: true }, { quebec: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post undefined: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post undefined matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post undefined available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post has no Province data: undefined

      at log (src/features/social-media/lib/utils.ts:8:13)

    console.log
      Post has no Province data: undefined

      at log (src/features/social-media/lib/utils.ts:8:13)

    console.log
      Checking post undefined for province: "britishcolumbia" (length: 15)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post undefined Province data: [ { britishColumbia: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post undefined: { britishColumbia: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "britishColumbia" (string) against userProvince: "britishcolumbia" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post undefined matches province britishColumbia for user britishcolumbia

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post undefined available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      getProvinceContent called with post: [ { ontario: true, images: [ [Object] ], captionTexts: [ [Object] ] } ]

      at log (src/features/social-media/lib/utils.ts:63:11)

    console.log
      User province: ontario

      at log (src/features/social-media/lib/utils.ts:64:11)

    console.log
      User province lowercase: ontario

      at log (src/features/social-media/lib/utils.ts:68:11)

    console.log
      User province original: ontario

      at log (src/features/social-media/lib/utils.ts:69:11)

    console.log
      Available province keys: undefined

      at log (src/features/social-media/lib/utils.ts:70:11)

    console.log
      Looking for province match in: undefined

      at log (src/features/social-media/lib/utils.ts:73:11)

    console.log
      User province to match: ontario

      at log (src/features/social-media/lib/utils.ts:74:11)

    console.log
      getProvinceContent called with post: [ { ontario: true, images: [ [Object] ], captionTexts: [ [Object] ] } ]

      at log (src/features/social-media/lib/utils.ts:63:11)

    console.log
      User province: quebec

      at log (src/features/social-media/lib/utils.ts:64:11)

    console.log
      User province lowercase: quebec

      at log (src/features/social-media/lib/utils.ts:68:11)

    console.log
      User province original: quebec

      at log (src/features/social-media/lib/utils.ts:69:11)

    console.log
      Available province keys: undefined

      at log (src/features/social-media/lib/utils.ts:70:11)

    console.log
      Looking for province match in: undefined

      at log (src/features/social-media/lib/utils.ts:73:11)

    console.log
      User province to match: quebec

      at log (src/features/social-media/lib/utils.ts:74:11)

    console.log
      getProvinceContent called with post: [ { all: true, images: [ [Object] ], captionTexts: [ [Object] ] } ]

      at log (src/features/social-media/lib/utils.ts:63:11)

    console.log
      User province: ontario

      at log (src/features/social-media/lib/utils.ts:64:11)

    console.log
      User province lowercase: ontario

      at log (src/features/social-media/lib/utils.ts:68:11)

    console.log
      User province original: ontario

      at log (src/features/social-media/lib/utils.ts:69:11)

    console.log
      Available province keys: undefined

      at log (src/features/social-media/lib/utils.ts:70:11)

    console.log
      Looking for province match in: undefined

      at log (src/features/social-media/lib/utils.ts:73:11)

    console.log
      User province to match: ontario

      at log (src/features/social-media/lib/utils.ts:74:11)

    console.log
      getProvinceContent called with post: [ { ontario: true, images: [ [Object] ], captionTexts: [ [Object] ] } ]

      at log (src/features/social-media/lib/utils.ts:63:11)

    console.log
      User province: ontario

      at log (src/features/social-media/lib/utils.ts:64:11)

    console.log
      User province lowercase: ontario

      at log (src/features/social-media/lib/utils.ts:68:11)

    console.log
      User province original: ontario

      at log (src/features/social-media/lib/utils.ts:69:11)

    console.log
      Available province keys: undefined

      at log (src/features/social-media/lib/utils.ts:70:11)

    console.log
      Looking for province match in: undefined

      at log (src/features/social-media/lib/utils.ts:73:11)

    console.log
      User province to match: ontario

      at log (src/features/social-media/lib/utils.ts:74:11)

    console.log
      getProvinceContent called with post: [ { ontario: true, images: [ [Object] ], captionTexts: [] } ]

      at log (src/features/social-media/lib/utils.ts:63:11)

    console.log
      User province: ontario

      at log (src/features/social-media/lib/utils.ts:64:11)

    console.log
      User province lowercase: ontario

      at log (src/features/social-media/lib/utils.ts:68:11)

    console.log
      User province original: ontario

      at log (src/features/social-media/lib/utils.ts:69:11)

    console.log
      Available province keys: undefined

      at log (src/features/social-media/lib/utils.ts:70:11)

    console.log
      Looking for province match in: undefined

      at log (src/features/social-media/lib/utils.ts:73:11)

    console.log
      User province to match: ontario

      at log (src/features/social-media/lib/utils.ts:74:11)

    console.log
      getProvinceContent called with post: [ { ontario: true, images: [], captionTexts: [ [Object] ] } ]

      at log (src/features/social-media/lib/utils.ts:63:11)

    console.log
      User province: ontario

      at log (src/features/social-media/lib/utils.ts:64:11)

    console.log
      User province lowercase: ontario

      at log (src/features/social-media/lib/utils.ts:68:11)

    console.log
      User province original: ontario

      at log (src/features/social-media/lib/utils.ts:69:11)

    console.log
      Available province keys: undefined

      at log (src/features/social-media/lib/utils.ts:70:11)

    console.log
      Looking for province match in: undefined

      at log (src/features/social-media/lib/utils.ts:73:11)

    console.log
      User province to match: ontario

      at log (src/features/social-media/lib/utils.ts:74:11)

 PASS  src/features/listing-sheet/hooks/__tests__/use-pdf-generator.test.ts (6.954 s)
 FAIL  src/features/social-media/__tests__/use-social-media-content.test.ts (8.276 s)
  ● Console

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:94:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:94:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:120:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:120:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:137:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:137:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:158:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:158:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:182:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:182:20)

    console.log
      Checking province availability for user: undefined

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "" (length: 0)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: false

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:204:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:204:20)

    console.log
      Checking province availability for user: undefined

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "" (length: 0)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: false

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:226:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:226:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:254:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:254:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:282:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:282:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 'invalid',
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 'invalid',
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: string value: invalid

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: false

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        Province: [ { ontario: true } ],
        extraTitle: 'Spring Forward Reminder(s)'
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: true,
        Province: [ { ontario: true } ],
        extraTitle: 'Spring Forward Reminder(s)'
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: true

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:329:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:329:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

    console.log
      Post documentId: doc1

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:31:13)

    console.log
      Post month: 2025-07-01

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:32:13)

    console.log
      Post isExtra: false

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:33:13)

    console.log
      Post Province: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:34:13)

    console.log
      Validating post: {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [ { ontario: true } ]
      }

      at log (src/features/social-media/lib/utils.ts:274:11)

    console.log
      Post exists: true

      at log (src/features/social-media/lib/utils.ts:275:11)

    console.log
      id type: number value: 1

      at log (src/features/social-media/lib/utils.ts:276:11)

    console.log
      documentId type: string value: doc1

      at log (src/features/social-media/lib/utils.ts:277:11)

    console.log
      month type: string value: 2025-07-01

      at log (src/features/social-media/lib/utils.ts:278:11)

    console.log
      isExtra type: boolean value: false

      at log (src/features/social-media/lib/utils.ts:279:11)

    console.log
      Province is array: true

      at log (src/features/social-media/lib/utils.ts:280:11)

    console.log
      Post validation result: true

      at log (src/features/social-media/lib/utils.ts:289:11)

    console.error
      Error: Not implemented: window.scrollTo
          at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
          at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:343:20)
          at new Promise (<anonymous>) {
        type: 'not implemented'
      }

      54 |       } else {
      55 |         // Fallback to scrolling to top of window
    > 56 |         window.scrollTo({ top: 0, behavior: 'smooth' });
         |                ^
      57 |       }
      58 |
      59 |       // First check if the lightweight post has province data for filtering

      at VirtualConsole.<anonymous> (node_modules/@jest/environment-jsdom-abstract/build/index.js:78:23)
      at module.exports (node_modules/jsdom/lib/jsdom/browser/not-implemented.js:12:26)
      at node_modules/jsdom/lib/jsdom/browser/Window.js:960:7
      at Object.scrollTo [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:56:16)
      at Object.setContent (src/features/social-media/__tests__/use-social-media-content.test.ts:343:20)

    console.log
      Checking province availability for user: ontario

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:65:15)

    console.log
      Post province data: [ { ontario: true } ]

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:66:15)

    console.log
      Checking post 1 for province: "ontario" (length: 7)

      at log (src/features/social-media/lib/utils.ts:13:11)

    console.log
      Post 1 Province data: [ { ontario: true } ]

      at log (src/features/social-media/lib/utils.ts:14:11)

    console.log
      Province object for post 1: { ontario: true }

      at log (src/features/social-media/lib/utils.ts:20:13)
          at Array.some (<anonymous>)

    console.log
      Checking key: "ontario" (string) against userProvince: "ontario" (string)

      at log (src/features/social-media/lib/utils.ts:44:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Key value: true (boolean)

      at log (src/features/social-media/lib/utils.ts:45:15)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 matches province ontario for user ontario

      at log (src/features/social-media/lib/utils.ts:49:17)
          at Array.some (<anonymous>)
          at Array.some (<anonymous>)

    console.log
      Post 1 available: true

      at log (src/features/social-media/lib/utils.ts:55:11)

    console.log
      Post available for province: true

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:69:15)

    console.log
      Post is available for province, fetching full content...

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:76:15)

    console.log
      handleSetContent called with post: null

      at Object.log [as setContent] (src/features/social-media/hooks/use-social-media-content.ts:22:13)

  ● useSocialMediaContent › fetches full post content when setContent is called

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: 1

    Number of calls: 0

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

       95 |
       96 |     await waitFor(() => {
    >  97 |       expect(mockApiClient.getSocialMediaPost).toHaveBeenCalledWith(1);
          |                                                ^
       98 |     });
       99 |
      100 |     expect(result.current.content).toEqual(mockFullPost);

      at toHaveBeenCalledWith (src/features/social-media/__tests__/use-social-media-content.test.ts:97:48)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › filters content by user province

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      121 |
      122 |     await waitFor(() => {
    > 123 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      124 |     });
      125 |
      126 |     // Should only show Ontario content

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:123:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles API errors gracefully

    expect(received).toContain(expected) // indexOf

    Expected substring: "Failed to fetch post"
    Received string:    "Cannot read properties of undefined (reading 'data')"

      141 |     });
      142 |
    > 143 |     expect(result.current.error).toContain(errorMessage);
          |                                  ^
      144 |     expect(result.current.isLoading).toBe(false);
      145 |     expect(result.current.content).toBeNull();
      146 |   });

      at Object.toContain (src/features/social-media/__tests__/use-social-media-content.test.ts:143:34)

  ● useSocialMediaContent › shows loading state while fetching

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      158 |     result.current.setContent(mockLightweightPost);
      159 |
    > 160 |     expect(result.current.isLoading).toBe(true);
          |                                      ^
      161 |     expect(result.current.error).toBeNull();
      162 |
      163 |     resolvePromise!({ data: mockFullPost });

      at Object.toBe (src/features/social-media/__tests__/use-social-media-content.test.ts:160:38)

  ● useSocialMediaContent › handles user without province gracefully

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      183 |
      184 |     await waitFor(() => {
    > 185 |       expect(mockApiClient.getSocialMediaPost).toHaveBeenCalled();
          |                                                ^
      186 |     });
      187 |
      188 |     // Should still fetch content even without province

      at toHaveBeenCalled (src/features/social-media/__tests__/use-social-media-content.test.ts:185:48)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles user without team gracefully

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      205 |
      206 |     await waitFor(() => {
    > 207 |       expect(mockApiClient.getSocialMediaPost).toHaveBeenCalled();
          |                                                ^
      208 |     });
      209 |
      210 |     // Should still fetch content even without team

      at toHaveBeenCalled (src/features/social-media/__tests__/use-social-media-content.test.ts:207:48)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles posts with missing Province data

    expect(received).toContain(expected) // indexOf

    Expected substring: "No content found for your province"
    Received string:    "Cannot read properties of undefined (reading 'data')"

      231 |
      232 |     // Should show error for posts without Province data
    > 233 |     expect(result.current.error).toContain('No content found for your province');
          |                                  ^
      234 |   });
      235 |
      236 |   it('handles posts with missing images gracefully', async () => {

      at Object.toContain (src/features/social-media/__tests__/use-social-media-content.test.ts:233:34)

  ● useSocialMediaContent › handles posts with missing images gracefully

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      255 |
      256 |     await waitFor(() => {
    > 257 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      258 |     });
      259 |
      260 |     // Should handle posts without images

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:257:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles posts with missing captions gracefully

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      283 |
      284 |     await waitFor(() => {
    > 285 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      286 |     });
      287 |
      288 |     // Should handle posts without captions

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:285:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles extra posts correctly

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      330 |
      331 |     await waitFor(() => {
    > 332 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      333 |     });
      334 |
      335 |     expect(result.current.content?.isExtra).toBe(true);

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:332:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

Summary of all failing tests
 FAIL  src/features/social-media/__tests__/use-social-media.test.ts
  ● useSocialMedia › fetches posts on mount when user is authenticated

    expect(received).toHaveLength(expected)

    Expected length: 2
    Received length: 0
    Received array:  []

      64 |     });
      65 |
    > 66 |     expect(result.current.monthlyPosts).toHaveLength(2);
         |                                         ^
      67 |     expect(result.current.extraPosts).toHaveLength(1);
      68 |     expect(result.current.isLoading).toBe(false);
      69 |     expect(result.current.error).toBeNull();

      at Object.toHaveLength (src/features/social-media/__tests__/use-social-media.test.ts:66:41)

  ● useSocialMedia › handles user without province gracefully

    expect(received).toHaveLength(expected)

    Expected length: 2
    Received length: 0
    Received array:  []

      158 |
      159 |     // Should still fetch posts even without province
    > 160 |     expect(result.current.monthlyPosts).toHaveLength(2);
          |                                         ^
      161 |     expect(result.current.extraPosts).toHaveLength(1);
      162 |   });
      163 |

      at Object.toHaveLength (src/features/social-media/__tests__/use-social-media.test.ts:160:41)

  ● useSocialMedia › handles user without team gracefully

    expect(received).toHaveLength(expected)

    Expected length: 2
    Received length: 0
    Received array:  []

      179 |
      180 |     // Should still fetch posts even without team
    > 181 |     expect(result.current.monthlyPosts).toHaveLength(2);
          |                                         ^
      182 |     expect(result.current.extraPosts).toHaveLength(1);
      183 |   });
      184 |

      at Object.toHaveLength (src/features/social-media/__tests__/use-social-media.test.ts:181:41)

  ● useSocialMedia › does not fetch posts when user is not authenticated

    expect(jest.fn()).not.toHaveBeenCalled()

    Expected number of calls: 0
    Received number of calls: 1

    1: called with 0 arguments

      210 |     renderHook(() => useSocialMedia());
      211 |
    > 212 |     expect(mockGetLightweightPosts).not.toHaveBeenCalled();
          |                                         ^
      213 |   });
      214 |
      215 |   it('handles empty posts response', async () => {

      at Object.toHaveBeenCalled (src/features/social-media/__tests__/use-social-media.test.ts:212:41)

 FAIL  src/features/social-media/__tests__/use-social-media-content.test.ts (8.276 s)
  ● useSocialMediaContent › fetches full post content when setContent is called

    expect(jest.fn()).toHaveBeenCalledWith(...expected)

    Expected: 1

    Number of calls: 0

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

       95 |
       96 |     await waitFor(() => {
    >  97 |       expect(mockApiClient.getSocialMediaPost).toHaveBeenCalledWith(1);
          |                                                ^
       98 |     });
       99 |
      100 |     expect(result.current.content).toEqual(mockFullPost);

      at toHaveBeenCalledWith (src/features/social-media/__tests__/use-social-media-content.test.ts:97:48)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › filters content by user province

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      121 |
      122 |     await waitFor(() => {
    > 123 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      124 |     });
      125 |
      126 |     // Should only show Ontario content

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:123:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles API errors gracefully

    expect(received).toContain(expected) // indexOf

    Expected substring: "Failed to fetch post"
    Received string:    "Cannot read properties of undefined (reading 'data')"

      141 |     });
      142 |
    > 143 |     expect(result.current.error).toContain(errorMessage);
          |                                  ^
      144 |     expect(result.current.isLoading).toBe(false);
      145 |     expect(result.current.content).toBeNull();
      146 |   });

      at Object.toContain (src/features/social-media/__tests__/use-social-media-content.test.ts:143:34)

  ● useSocialMediaContent › shows loading state while fetching

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      158 |     result.current.setContent(mockLightweightPost);
      159 |
    > 160 |     expect(result.current.isLoading).toBe(true);
          |                                      ^
      161 |     expect(result.current.error).toBeNull();
      162 |
      163 |     resolvePromise!({ data: mockFullPost });

      at Object.toBe (src/features/social-media/__tests__/use-social-media-content.test.ts:160:38)

  ● useSocialMediaContent › handles user without province gracefully

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      183 |
      184 |     await waitFor(() => {
    > 185 |       expect(mockApiClient.getSocialMediaPost).toHaveBeenCalled();
          |                                                ^
      186 |     });
      187 |
      188 |     // Should still fetch content even without province

      at toHaveBeenCalled (src/features/social-media/__tests__/use-social-media-content.test.ts:185:48)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles user without team gracefully

    expect(jest.fn()).toHaveBeenCalled()

    Expected number of calls: >= 1
    Received number of calls:    0

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      205 |
      206 |     await waitFor(() => {
    > 207 |       expect(mockApiClient.getSocialMediaPost).toHaveBeenCalled();
          |                                                ^
      208 |     });
      209 |
      210 |     // Should still fetch content even without team

      at toHaveBeenCalled (src/features/social-media/__tests__/use-social-media-content.test.ts:207:48)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles posts with missing Province data

    expect(received).toContain(expected) // indexOf

    Expected substring: "No content found for your province"
    Received string:    "Cannot read properties of undefined (reading 'data')"

      231 |
      232 |     // Should show error for posts without Province data
    > 233 |     expect(result.current.error).toContain('No content found for your province');
          |                                  ^
      234 |   });
      235 |
      236 |   it('handles posts with missing images gracefully', async () => {

      at Object.toContain (src/features/social-media/__tests__/use-social-media-content.test.ts:233:34)

  ● useSocialMediaContent › handles posts with missing images gracefully

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      255 |
      256 |     await waitFor(() => {
    > 257 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      258 |     });
      259 |
      260 |     // Should handle posts without images

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:257:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles posts with missing captions gracefully

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      283 |
      284 |     await waitFor(() => {
    > 285 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      286 |     });
      287 |
      288 |     // Should handle posts without captions

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:285:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)

  ● useSocialMediaContent › handles extra posts correctly

    expect(received).toBeTruthy()

    Received: null

    Ignored nodes: comments, script, style
    <html>
      <head />
      <body>
        <div />
      </body>
    </html>

      330 |
      331 |     await waitFor(() => {
    > 332 |       expect(result.current.content).toBeTruthy();
          |                                      ^
      333 |     });
      334 |
      335 |     expect(result.current.content?.isExtra).toBe(true);

      at toBeTruthy (src/features/social-media/__tests__/use-social-media-content.test.ts:332:38)
      at runWithExpensiveErrorDiagnosticsDisabled (node_modules/@testing-library/dom/dist/config.js:47:12)
      at checkCallback (node_modules/@testing-library/dom/dist/wait-for.js:124:77)
      at checkRealTimersCallback (node_modules/@testing-library/dom/dist/wait-for.js:118:16)
      at Timeout.task [as _onTimeout] (node_modules/jsdom/lib/jsdom/browser/Window.js:579:19)


Test Suites: 2 failed, 2 skipped, 56 passed, 58 of 60 total
Tests:       14 failed, 16 skipped, 518 passed, 548 total
Snapshots:   0 total
Time:        9.13 s
Ran all test suites.