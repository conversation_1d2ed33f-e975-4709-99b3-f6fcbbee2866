"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import { cn } from "@/lib/utils";
import { Input } from "../ui";
import { Search } from "lucide-react";

export interface CategoryItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: {
    url: string;
    alternativeText?: string;
    formats?: {
      thumbnail?: { url: string };
    };
  };
  file?: {
    url: string;
    ext?: string;
    name?: string;
    mime?: string;
    size?: number;
  };
  metadata?: Record<string, any>;
  category?: string;
  tags?: string[];
}

export interface CategoryItemsListProps {
  title: string;
  subtitle?: string;
  items: CategoryItem[];
  isLoading?: boolean;
  error?: string | null;
  onItemSelect?: (item: CategoryItem) => void;
  buttonLabel?: string;
  layout?: "grid" | "list";
  className?: string;
  emptyStateMessage?: string;
  retryAction?: () => void;
  showSearchAndFilter?: boolean;
  onSearch?: (query: string) => void;
  onFilterChange?: (filter: string) => void;
  availableFilters?: string[];
}

const CategoryItemsList: React.FC<CategoryItemsListProps> = ({
  title,
  subtitle,
  items,
  isLoading = false,
  error = null,
  onItemSelect,
  buttonLabel = "View Item",
  layout = "grid",
  className,
  emptyStateMessage = "No items found",
  retryAction,
  showSearchAndFilter = false,
  onSearch,
  onFilterChange,
  availableFilters = [],
}) => {
  const handleItemClick = React.useCallback((item: CategoryItem) => {
    try {
      if (onItemSelect) {
        onItemSelect(item);
      }
    } catch (error) {
      console.error("Error handling item selection:", error);
      // Fallback: try to handle the error gracefully
      if (item.file?.url) {
        console.log("Attempting fallback navigation for item:", item.title);
        // You could implement a fallback navigation here if needed
      }
    }
  }, [onItemSelect]);

  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, item: CategoryItem) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleItemClick(item);
    }
  }, [handleItemClick]);

  const handleButtonClick = React.useCallback((event: React.MouseEvent, item: CategoryItem) => {
    event.stopPropagation();
    handleItemClick(item);
  }, [handleItemClick]);

  const handleSearchChange = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    if (onSearch) {
      onSearch(event.target.value);
    }
  }, [onSearch]);

  const handleFilterChange = React.useCallback((value: string) => {
    if (onFilterChange) {
      // Convert "all" back to empty string for the parent component
      const filterValue = value === "all" ? "" : value;
      onFilterChange(filterValue);
    }
  }, [onFilterChange]);

  const getThumbnailUrl = React.useCallback((thumbnail: CategoryItem["thumbnail"]) => {
    if (!thumbnail) return null;
    return thumbnail.formats?.thumbnail?.url || thumbnail.url;
  }, []);


  // Loading skeleton
  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/3" />
          {subtitle && <Skeleton className="h-6 w-1/2" />}
        </div>

        <div className={cn(
          layout === "grid"
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
        )}>
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="aspect-video w-full rounded-lg" />
              <div className="space-y-2 p-4">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-3 w-1/3" />
              </div>
              <Skeleton className="h-9 w-full mx-4 mb-4" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-12", className)}>
        <div className="text-center">
          <svg
            className="mx-auto h-16 w-16 text-muted-foreground mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <h3 className="text-lg font-medium text-foreground mb-2">
            Something went wrong
          </h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          {retryAction && (
            <Button onClick={retryAction} variant="outline">
              <svg
                className="h-4 w-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Try again
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Empty state
  if (items.length === 0) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="text-center py-12">
          <svg
            className="mx-auto h-16 w-16 text-muted-foreground mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <h3 className="text-lg font-medium text-foreground mb-2">
            {emptyStateMessage}
          </h3>
          {retryAction && (
            <Button onClick={retryAction} variant="outline">
              Refresh
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header Section */}
      <div className="space-y-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          {subtitle && (
            <p className="text-xl text-muted-foreground mt-2">{subtitle}</p>
          )}
        </div>

        {/* Search and Filter Section */}
        {showSearchAndFilter && (
          <Card>
            <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            {onSearch && (
              <div className="relative flex-1 max-w-sm spayce-y-2">
              
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search items..."
                  onChange={handleSearchChange}
                  className="pl-10"
                />
              </div>
            )}            
          </div>
          </CardContent>
          </Card>
        )}

        {/* Items Count */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {items.length} {items.length === 1 ? "item" : "items"} found
          </p>
        </div>
      </div>

      {/* Items Grid/List */}
      <div className={cn(
        layout === "grid"
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          : "space-y-4"
      )}>
        {items.map((item) => (
          <Card
            key={item.id}
            className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] p-0 overflow-hidden flex flex-col justify-between gap-2"
            onClick={() => handleItemClick(item)}
            onKeyDown={(e) => handleKeyDown(e, item)}
            tabIndex={0}
            role="button"
            aria-label={`View ${item.title}`}
          >
            <CardContent className="p-0">
              {/* Thumbnail */}
              <div className="relative aspect-video overflow-hidden rounded-t-lg bg-muted">
                {getThumbnailUrl(item.thumbnail) ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <img
                      src={getThumbnailUrl(item.thumbnail)!}
                      alt={item.thumbnail?.alternativeText || item.title}
                      className="w-auto h-full py-2"
                    />
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <svg
                      className="h-16 w-16 text-primary/60"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                )}                
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-lg leading-tight line-clamp-2 group-hover:text-primary transition-colors">
                  {item.title}
                </h3>  

                <div className="flex gap-2 mt-2">
                {/* File type badge */}
                {item.file?.ext && (
                  <div className="">
                    <Badge variant="outline" className="capitalize">
                      {item.file.ext.replace(".", "")}
                    </Badge>
                  </div>
                )}

                {/* Category badge */}
                {item.category && (
                  <div className="">
                    <Badge variant="outline">
                      {item.category}
                    </Badge>
                  </div>
                )}    
                </div>         

                {/* Metadata */}
                <div className="space-y-1">                  
                  {item.tags && item.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {item.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {item.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{item.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>

            {/* Footer with Button */}
            <CardFooter className="p-4 pt-0">
              <Button
                className="w-full"
                variant="default"
                onClick={(e) => handleButtonClick(e, item)}
              >
                {buttonLabel}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default CategoryItemsList;
