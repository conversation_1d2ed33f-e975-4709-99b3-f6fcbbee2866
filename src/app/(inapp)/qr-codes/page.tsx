"use client";

import { useAuthContext } from "@/shared/contexts/auth-context";
import { QRCodeForm } from "@/features/qr-codes/components/qr-code-form";
import { QRCodeList } from "@/features/qr-codes/components/qr-code-list";
import type { QRCode } from "@/features/qr-codes/types";

export default function QRCodesPage() {
  const { userAuth } = useAuthContext();

  // Get QR codes from user data
  const qrCodes: QRCode[] = userAuth.userInfo?.qrCodes || [];

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">QR Codes</h1>
        <p className="text-muted-foreground">
          Generate and manage QR codes for your links
        </p>
      </div>

      <QRCodeForm initialQRCodes={qrCodes} />

      <QRCodeList qrCodes={qrCodes} />
    </div>
  );
}
