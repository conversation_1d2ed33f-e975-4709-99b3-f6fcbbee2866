"use client";

import React, { useState } from "react";
import { useIndiSites } from "@/features/indi-sites/hooks/use-indi-sites";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/shared/ui/tabs";
import { WebsiteInfoForm } from "@/features/indi-sites/components/website-info-form";
import { SocialLinksForm } from "@/features/indi-sites/components/social-links-form";
import { HtmlTagsForm } from "@/features/indi-sites/components/html-tags-form";
import type {
  WebsiteInfoFormData,
  SocialLinksFormData,
  HtmlTagsFormData,
} from "@/features/indi-sites/lib/validation";

export default function IndiSitesPage() {
  const { 
    user, 
    website, 
    isLoading, 
    error, 
    updateWebsiteInfo,
    updateSocialLinks,
    updateHtmlTags
  } = useIndiSites();
  
  const [selectedTab, setSelectedTab] = useState("website-info");

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading your site information...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-red-600">Error loading site information: {error}</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-muted-foreground">No user data available.</p>
        </div>
      </div>
    );
  }

  // Handle website info form submission
  const handleWebsiteInfoSubmit = async (data: WebsiteInfoFormData) => {
    try {
      await updateWebsiteInfo(data);
    } catch (error) {
      // Error is handled by the form component
      throw error;
    }
  };

  // Handle social links form submission
  const handleSocialLinksSubmit = async (data: SocialLinksFormData) => {
    try {
      await updateSocialLinks(data);
    } catch (error) {
      // Error is handled by the form component
      throw error;
    }
  };

  // Handle HTML tags form submission
  const handleHtmlTagsSubmit = async (data: HtmlTagsFormData) => {
    try {
      await updateHtmlTags(data);
    } catch (error) {
      // Error is handled by the form component
      throw error;
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">My Indi Site</h1>
        <p className="text-muted-foreground">
          Manage your website information and HTML tags
        </p>
      </div>

      {/* Tabs */}
      <Tabs
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="website-info">Website Info</TabsTrigger>
          <TabsTrigger value="social-links">Social & Links</TabsTrigger>
          <TabsTrigger value="html-tags">HTML Tags</TabsTrigger>
        </TabsList>

        {/* Website Info Tab */}
        <TabsContent value="website-info" className="space-y-6">
          <WebsiteInfoForm
            user={user}
            onSubmit={handleWebsiteInfoSubmit}
          />
        </TabsContent>

        {/* Social Links Tab */}
        <TabsContent value="social-links" className="space-y-6">
          <SocialLinksForm
            user={user}
            onSubmit={handleSocialLinksSubmit}
          />
        </TabsContent>

        {/* HTML Tags Tab */}
        <TabsContent value="html-tags" className="space-y-6">
          <HtmlTagsForm
            user={user}
            website={website}
            onSubmit={handleHtmlTagsSubmit}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
