"use client";

import { useTechnology } from "@/features/technology/hooks/use-technology";
import { TechnologyCard } from "@/features/technology/components/technology-card";

export default function TechnologyPage() {
  const { data: technologies, isLoading, error } = useTechnology();

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading technology tools...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-red-600">Error loading technology tools: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Technology</h1>
        <p className="text-muted-foreground">
          Access your technology tools and platforms
        </p>
      </div>

      {/* Technologies Grid */}
      {technologies && technologies.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {technologies.map((technology) => (
            <TechnologyCard key={technology.id} technology={technology} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No technology tools are currently available.</p>
        </div>
      )}
    </div>
  );
}
