"use client";

import { useGroupBenefits } from "@/features/group-benefits/hooks/use-group-benefits";
import { BenefitsFileCard } from "@/features/group-benefits/components/benefits-file-card";
import { BodyTextRenderer } from "@/shared/components/body-text-renderer";

export default function GroupBenefitsPage() {
  const { data: pageData, isLoading, error } = useGroupBenefits();

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading group benefits...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-red-600">Error loading group benefits: {error}</p>
        </div>
      </div>
    );
  }

  if (!pageData) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-muted-foreground">No group benefits data available.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{pageData.pageTitle}</h1>
      </div>

      {/* Description */}
      {pageData.description && (
        <div className="prose prose-gray max-w-none">
          <BodyTextRenderer content={pageData.description} />
        </div>
      )}

      {/* Files Grid */}
      {pageData.files && pageData.files.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold">Available Documents</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {pageData.files.map((file) => (
              <BenefitsFileCard key={file.id} file={file} />
            ))}
          </div>
        </div>
      )}

      {/* No files message */}
      {(!pageData.files || pageData.files.length === 0) && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No documents are currently available.</p>
        </div>
      )}
    </div>
  );
}
