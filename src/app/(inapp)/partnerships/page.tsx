"use client";

import Link from "next/link";
import { usePartnerships } from "@/features/partnerships/hooks/use-partnerships";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { ExternalLink } from "lucide-react";

export default function PartnershipsPage() {
  const { data: partnerships, isLoading, error } = usePartnerships();

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading partnerships...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center py-12">
          <p className="text-red-600">Error loading partnerships: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Partnerships</h1>
        <p className="text-muted-foreground">
          Explore our partnership opportunities and resources
        </p>
      </div>

      {/* Partnerships Grid */}
      {partnerships && partnerships.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {partnerships.map((partnership) => (
            <Card key={partnership.id} className="h-full hover:shadow-md transition-shadow">
              <CardHeader>
                {/* Banner Image */}
                {partnership.banner && (
                  <div className="w-full h-48 mb-4 overflow-hidden rounded-lg">
                    <img
                      src={partnership.banner.formats?.squared?.url || partnership.banner.url}
                      alt={partnership.banner.alternativeText || partnership.Title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <CardTitle className="text-lg">{partnership.Title}</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col justify-between">
                {/* Content Preview */}
                {partnership.content && (
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {partnership.content.replace(/<[^>]*>/g, '').substring(0, 150)}...
                    </p>
                  </div>
                )}

                {/* Action Button */}
                <div className="mt-auto">
                  <Link href={`/partnerships/${partnership.slug}`} passHref>
                    <Button className="w-full" variant="default">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No partnerships are currently available.</p>
        </div>
      )}
    </div>
  );
}
