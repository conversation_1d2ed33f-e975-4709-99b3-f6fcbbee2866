"use client";

import { RealtorForm } from "@/features/realtors/components/realtor-form";
import { RealtorList } from "@/features/realtors/components/realtor-list";
import { useRealtors } from "@/features/realtors/hooks/use-realtors";
import { Button } from "@/shared/ui/button";
import { Loader2, RefreshCw } from "lucide-react";

export default function RealtorsPage() {
  const { realtors, isLoading, fetchRealtors } = useRealtors();

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading realtors...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Realtors</h1>
          <p className="text-muted-foreground">
            Add and manage your realtor contacts
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchRealtors}
          disabled={isLoading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <RealtorForm initialRealtors={realtors} onRealtorAdded={fetchRealtors} />

      <RealtorList realtors={realtors} onRealtorDeleted={fetchRealtors} onRealtorUpdated={fetchRealtors} />
    </div>
  );
}
