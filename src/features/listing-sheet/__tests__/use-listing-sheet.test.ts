import { renderHook } from '@testing-library/react';
import { useListingSheet } from '../hooks/use-listing-sheet';

jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

describe('useListingSheet', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useListingSheet());

    expect(result.current.isLoading).toBe(true);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useListingSheet());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useListingSheet());

    expect(() => result.current.retry()).not.toThrow();
  });
});
