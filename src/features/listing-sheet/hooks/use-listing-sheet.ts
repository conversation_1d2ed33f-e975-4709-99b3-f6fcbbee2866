import { useState, useEffect } from 'react';

export interface ListingSheetData {
  id: string;
  title: string;
  description: string;
  price: number;
  address: string;
  bedrooms: number;
  bathrooms: number;
  squareFootage: number;
  images: {
    id: string;
    url: string;
    alt: string;
  }[];
  features: string[];
  agent: {
    name: string;
    email: string;
    phone: string;
    photo?: {
      url: string;
    };
  };
}

export interface ListingSheetFilters {
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
  bathrooms?: number;
}

export const useListingSheet = (listingId?: string, filters: ListingSheetFilters = {}) => {
  const [data, setData] = useState<ListingSheetData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Add a small delay to ensure loading state is visible in tests
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // Mock implementation for testing
      const mockData: ListingSheetData = {
        id: listingId || '1',
        title: 'Beautiful Family Home',
        description: 'A stunning 3-bedroom home in a quiet neighborhood with modern amenities and a spacious backyard.',
        price: 450000,
        address: '123 Main Street, Toronto, ON M1A 1A1',
        bedrooms: 3,
        bathrooms: 2,
        squareFootage: 1800,
        images: [
          {
            id: '1',
            url: '/images/listing1.jpg',
            alt: 'Front view of the house'
          },
          {
            id: '2',
            url: '/images/listing2.jpg',
            alt: 'Living room'
          }
        ],
        features: [
          'Hardwood floors',
          'Updated kitchen',
          'Finished basement',
          'Large backyard',
          'Garage parking'
        ],
        agent: {
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '555-0123',
          photo: {
            url: '/images/agent.jpg'
          }
        }
      };

      // Apply filters if provided
      if (filters.minPrice && mockData.price < filters.minPrice) {
        throw new Error('No listings found matching criteria');
      }
      if (filters.maxPrice && mockData.price > filters.maxPrice) {
        throw new Error('No listings found matching criteria');
      }
      if (filters.bedrooms && mockData.bedrooms !== filters.bedrooms) {
        throw new Error('No listings found matching criteria');
      }
      if (filters.bathrooms && mockData.bathrooms !== filters.bathrooms) {
        throw new Error('No listings found matching criteria');
      }

      setData(mockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch listing sheet');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [listingId, filters.search, filters.minPrice, filters.maxPrice, filters.bedrooms, filters.bathrooms]);

  const retry = () => {
    fetchData();
  };

  return {
    data,
    isLoading,
    error,
    retry
  };
};
