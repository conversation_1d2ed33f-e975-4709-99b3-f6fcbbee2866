"use client";

import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/shared/hooks/use-auth';
import { socialMediaApi } from '../lib/api-client';
import { 
  isPostAvailableForProvince, 
  sortPostsByMonth, 
  filterPostsByType 
} from '../lib/utils';
import type { 
  UseSocialMediaResult, 
  LightweightPost 
} from '../types';

export const useSocialMedia = (): UseSocialMediaResult => {
  const [posts, setPosts] = useState<LightweightPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, isLoading: authLoading } = useAuth();
  
  console.log('useSocialMedia hook - user:', user);
  console.log('useSocialMedia hook - authLoading:', authLoading);

  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('Fetching social media posts...');
      const response = await socialMediaApi.getLightweightPosts();
      console.log('API Response:', response);
      console.log('Raw response data:', JSON.stringify(response, null, 2));
      
      const posts = response.data || [];
      console.log('Processed posts:', posts);
      console.log('First post sample:', posts[0]);
      console.log('Posts length:', posts.length);
      
      // Debug: Check if posts have Province data
      if (posts.length > 0) {
        console.log('First post Province structure:', posts[0]?.Province);
        console.log('First post Province type:', typeof posts[0]?.Province);
        console.log('First post Province is array:', Array.isArray(posts[0]?.Province));
      }
      
      setPosts(posts);
    } catch (err) {
      console.error('Error fetching social media posts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch posts');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch posts when user is loaded and authenticated
    if (!authLoading && user !== undefined && user !== null) {
      console.log('User loaded, fetching posts...');
      fetchPosts();
    } else if (authLoading) {
      console.log('Auth still loading, waiting...');
    } else {
      console.log('No user found, not fetching posts');
    }
  }, [authLoading, user]);

  // Memoize filtered posts based on user's province
           const { monthlyPosts, extraPosts } = useMemo(() => {
           if (!posts.length) {
             console.log('No posts to process');
             return { monthlyPosts: [], extraPosts: [] };
           }

           const userProvince = user?.province || user?.team?.province;
           console.log('User province:', userProvince);
           console.log('User object:', user);
           console.log('User team:', user?.team);
           console.log('User province direct:', user?.province);
           console.log('User team province:', user?.team?.province);
           
           // If user has no province, show all posts as fallback
           let availablePosts;
           if (!userProvince || userProvince.trim() === '') {
             console.log('No user province found, showing all posts as fallback');
             availablePosts = posts;
           } else {
             const filteredPosts = posts.filter(post =>
               isPostAvailableForProvince(post, userProvince)
             );
             console.log('Posts after province filtering:', filteredPosts.length);
             // If no posts match province, fallback to showing all posts for testing
             availablePosts = filteredPosts.length > 0 ? filteredPosts : posts;
           }
           console.log('Available posts after province filtering:', availablePosts.length);

           const monthly = sortPostsByMonth(filterPostsByType(availablePosts, false));
           const extra = filterPostsByType(availablePosts, true);
           
           console.log('Filtered posts:', { monthly: monthly.length, extra: extra.length });
           
           return {
             monthlyPosts: monthly,
             extraPosts: extra
           };
         }, [posts, user?.province, user?.team?.province]);

  const refetch = async () => {
    await fetchPosts();
  };

  return {
    monthlyPosts,
    extraPosts,
    isLoading,
    error,
    refetch
  };
};
