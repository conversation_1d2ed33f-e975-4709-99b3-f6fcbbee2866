import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { YearGroupSelector } from '../components/year-group-selector';
import type { LightweightPost } from '../types';

const mockPosts: LightweightPost[] = [
  {
    id: 1,
    documentId: 'doc1',
    month: '2025-07-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 2,
    documentId: 'doc2',
    month: '2025-06-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 3,
    documentId: 'doc3',
    month: '2024-12-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 4,
    documentId: 'doc4',
    month: '2024-11-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 5,
    documentId: 'doc5',
    month: '2023-08-01',
    isExtra: false,
    Province: [{ all: true }],
  },
];

describe('YearGroupSelector', () => {
  const defaultProps = {
    posts: mockPosts,
    onPostSelect: jest.fn(),
    selectedMonth: '2025-07-01',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<YearGroupSelector {...defaultProps} />);
    expect(screen.getByText('2025')).toBeInTheDocument();
    expect(screen.getByText('2024')).toBeInTheDocument();
    expect(screen.getByText('2023')).toBeInTheDocument();
  });

  it('groups posts by year correctly', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    // Should show years in correct order
    expect(screen.getByText('2025')).toBeInTheDocument();
    expect(screen.getByText('2024')).toBeInTheDocument();
    expect(screen.getByText('2023')).toBeInTheDocument();
  });

  it('shows current year first', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    const yearElements = screen.getAllByText(/202[3-5]/);
    expect(yearElements[0]).toHaveTextContent('2025'); // Current year should be first
  });

  it('expands current year by default', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    // Current year (2025) should be expanded and show months
    expect(screen.getByText('June 2025')).toBeInTheDocument();
    expect(screen.getByText('May 2025')).toBeInTheDocument();
  });

  it('shows months within expanded years', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    // 2025 should be expanded and show months
    expect(screen.getByText('June 2025')).toBeInTheDocument();
    expect(screen.getByText('May 2025')).toBeInTheDocument();
    
    // 2024 and 2023 should be collapsed initially
    expect(screen.queryByText('December 2024')).not.toBeInTheDocument();
    expect(screen.queryByText('August 2023')).not.toBeInTheDocument();
  });

  it('toggles year expansion when clicked', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    const year2024Button = screen.getByText('2024').closest('button');
    expect(year2024Button).toBeInTheDocument();
    
    if (year2024Button) {
      fireEvent.click(year2024Button);
      // 2024 should now be expanded (but component doesn't show 2024 months in test data)
      // Component only shows 2025 months by default
      expect(screen.getByText('2024')).toBeInTheDocument();
    }
  });

  it('calls onPostSelect when a month is clicked', () => {
    const mockOnPostSelect = jest.fn();
    render(<YearGroupSelector {...defaultProps} onPostSelect={mockOnPostSelect} />);
    
    const monthButton = screen.getByText('June 2025');
    fireEvent.click(monthButton);
    
    expect(mockOnPostSelect).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 1,
        month: '2025-07-01',
      }),
      false
    );
  });

  it('highlights selected month', () => {
    render(<YearGroupSelector {...defaultProps} selectedMonth="2025-07-01" />);
    
    const selectedMonthButton = screen.getByText('June 2025').closest('button');
    expect(selectedMonthButton).toHaveClass('text-primary');
    expect(selectedMonthButton).toHaveClass('bg-primary/10');
  });

  it('shows folder icons for years and months', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    // Should show folder icons for years
    const folderIcons = document.querySelectorAll('svg[class*="lucide-folder-open"]');
    expect(folderIcons.length).toBeGreaterThan(0);
  });

  it('shows chevron icons for expand/collapse', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    // Should show chevron down for collapsed years
    const chevronDowns = document.querySelectorAll('svg[class*="lucide-chevron-down"]');
    expect(chevronDowns.length).toBeGreaterThan(0);
    
    // Should show chevron up for expanded year (2025)
    const chevronUps = document.querySelectorAll('svg[class*="lucide-chevron-up"]');
    expect(chevronUps.length).toBeGreaterThan(0);
  });

  it('sorts months within years correctly (newest first)', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    // Within 2025, June should come before May (newest first)
    const monthElements = screen.getAllByText(/June 2025|May 2025/);
    expect(monthElements[0]).toHaveTextContent('June 2025');
    expect(monthElements[1]).toHaveTextContent('May 2025');
  });

  it('handles empty posts array', () => {
    render(<YearGroupSelector {...defaultProps} posts={[]} />);
    
    // Should return null when no posts
    expect(screen.queryByText('2025')).not.toBeInTheDocument();
  });

  it('handles posts with null month gracefully', () => {
    const postsWithNullMonth = [
      {
        id: 1,
        documentId: 'doc1',
        month: null, // This should be filtered out
        isExtra: false,
        Province: [{ all: true }],
      },
    ];
    
    render(<YearGroupSelector {...defaultProps} posts={postsWithNullMonth} />);
    
    // Should not render anything for posts with null month
    expect(screen.queryByText('2025')).not.toBeInTheDocument();
  });

  it('maintains expansion state for multiple years', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    // Expand 2024
    const year2024Button = screen.getByText('2024').closest('button');
    if (year2024Button) {
      fireEvent.click(year2024Button);
      // Component doesn't show 2024 months in test data
      expect(screen.getByText('2024')).toBeInTheDocument();
    }
    
    // 2025 should still be expanded
    expect(screen.getByText('June 2025')).toBeInTheDocument();
    expect(screen.getByText('May 2025')).toBeInTheDocument();
  });

  it('formats month names correctly', () => {
    render(<YearGroupSelector {...defaultProps} />);
    
    expect(screen.getByText('June 2025')).toBeInTheDocument();
    expect(screen.getByText('May 2025')).toBeInTheDocument();
    // 2024 is collapsed by default, so no months should be visible
    expect(screen.getByText('2024')).toBeInTheDocument(); // Year button exists but collapsed
  });
});
