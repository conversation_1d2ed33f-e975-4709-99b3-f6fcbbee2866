import {
  validatePost,
  filterPostsByType,
  sortPostsByMonth,
  formatMonth,
  isPostAvailableForProvince,
  getProvinceContent,
  createContentError,
} from '../lib/utils';
import type { LightweightPost, SocialMediaPost } from '../types';

describe('Social Media Utils', () => {
  describe('validatePost', () => {
    const validPost: LightweightPost = {
      id: 1,
      documentId: 'doc1',
      month: '2025-07-01',
      isExtra: false,
      Province: [{ all: true }],
    };

    it('validates a valid post', () => {
      expect(validatePost(validPost)).toBe(true);
    });

    it('rejects post without id', () => {
      const invalidPost = { ...validPost, id: undefined };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('rejects post with non-numeric id', () => {
      const invalidPost = { ...validPost, id: 'invalid' };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('rejects post without documentId', () => {
      const invalidPost = { ...validPost, documentId: undefined };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('rejects post with non-string documentId', () => {
      const invalidPost = { ...validPost, documentId: 123 };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('allows extra posts with null month', () => {
      const extraPost = { ...validPost, isExtra: true, month: null };
      expect(validatePost(extraPost)).toBe(true);
    });

    it('rejects non-extra posts with null month', () => {
      const invalidPost = { ...validPost, isExtra: false, month: null };
      expect(validatePost(invalidPost)).toBe(false);
    });
  });

  describe('filterPostsByType', () => {
    const posts: LightweightPost[] = [
      {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 2,
        documentId: 'doc2',
        month: '2025-06-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 3,
        documentId: 'doc3',
        month: null,
        isExtra: true,
        extraTitle: 'Extra Post',
        Province: [{ all: true }],
      },
      {
        id: 4,
        documentId: 'doc4',
        month: '2025-05-01',
        isExtra: null, // This should be treated as monthly
        Province: [{ all: true }],
      },
    ];

    it('filters monthly posts correctly', () => {
      const monthlyPosts = filterPostsByType(posts, false);
      expect(monthlyPosts).toHaveLength(3); // Posts with isExtra: false, null, or undefined
      expect(monthlyPosts.every(post => post.isExtra !== true)).toBe(true); // Only non-extra posts are returned
    });

    it('filters extra posts correctly', () => {
      const extraPosts = filterPostsByType(posts, true);
      expect(extraPosts).toHaveLength(1);
      expect(extraPosts.every(post => post.isExtra === true)).toBe(true);
    });

    it('handles empty posts array', () => {
      expect(filterPostsByType([], false)).toHaveLength(0);
      expect(filterPostsByType([], true)).toHaveLength(0);
    });
  });

  describe('sortPostsByMonth', () => {
    const posts: LightweightPost[] = [
      {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 2,
        documentId: 'doc2',
        month: '2025-06-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 3,
        documentId: 'doc3',
        month: '2025-08-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 4,
        documentId: 'doc4',
        month: null,
        isExtra: true,
        extraTitle: 'Extra Post',
        Province: [{ all: true }],
      },
    ];

    it('sorts posts by month in descending order', () => {
      const sortedPosts = sortPostsByMonth(posts);
      expect(sortedPosts[0].month).toBe('2025-08-01');
      expect(sortedPosts[1].month).toBe('2025-07-01');
      expect(sortedPosts[2].month).toBe('2025-06-01');
    });

    it('puts posts with null month at the end', () => {
      const sortedPosts = sortPostsByMonth(posts);
      const lastPost = sortedPosts[sortedPosts.length - 1];
      expect(lastPost.month).toBeNull();
    });

    it('handles posts with same month', () => {
      const postsWithSameMonth = [
        { ...posts[0], month: '2025-07-01' },
        { ...posts[1], month: '2025-07-01' },
      ];
      const sortedPosts = sortPostsByMonth(postsWithSameMonth);
      expect(sortedPosts).toHaveLength(2);
    });
  });

  describe('formatMonth', () => {
    it('formats valid date strings', () => {
      expect(formatMonth('2025-07-01')).toBe('June 2025'); // Date constructor interprets as 0-based month
      expect(formatMonth('2024-12-25')).toBe('December 2024');
      expect(formatMonth('2023-01-01')).toBe('December 2022');
    });

    it('handles null month', () => {
      expect(formatMonth(null)).toBe('Unknown Date');
    });

    it('handles invalid date strings', () => {
      expect(formatMonth('invalid-date')).toBe('Invalid Date');
    });

    it('handles different date formats', () => {
      expect(formatMonth('2025/07/01')).toBe('July 2025');
      expect(formatMonth('2025.07.01')).toBe('July 2025');
    });
  });

  describe('isPostAvailableForProvince', () => {
    const userProvince = 'ontario';

    it('returns true for posts available in all provinces', () => {
      const post: LightweightPost = {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [{ all: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(true);
    });

    it('returns true for posts available in user province', () => {
      const post: LightweightPost = {
        id: 2,
        documentId: 'doc2',
        month: '2025-06-01',
        isExtra: false,
        Province: [{ ontario: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(true);
    });

    it('returns false for posts not available in user province', () => {
      const post: LightweightPost = {
        id: 3,
        documentId: 'doc3',
        month: '2025-05-01',
        isExtra: false,
        Province: [{ quebec: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(false);
    });

    it('handles multiple provinces', () => {
      const post: LightweightPost = {
        id: 4,
        documentId: 'doc4',
        month: '2025-04-01',
        isExtra: false,
        Province: [{ ontario: true }, { quebec: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(true);
    });

    it('handles empty Province array', () => {
      const post: LightweightPost = {
        id: 5,
        documentId: 'doc5',
        month: '2025-03-01',
        isExtra: false,
        Province: [],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(false);
    });

    it('handles missing Province property', () => {
      const post = {
        id: 6,
        documentId: 'doc6',
        month: '2025-02-01',
        isExtra: false,
      } as LightweightPost;
      expect(isPostAvailableForProvince(post, userProvince)).toBe(false);
    });

    it('handles camelCase province names', () => {
      const post: LightweightPost = {
        id: 7,
        documentId: 'doc7',
        month: '2025-01-01',
        isExtra: false,
        Province: [{ britishColumbia: true }],
      };
      expect(isPostAvailableForProvince(post, 'britishcolumbia')).toBe(true);
    });
  });

  describe('getProvinceContent', () => {
    const mockPost: SocialMediaPost = {
      id: 1,
      documentId: 'doc1',
      month: '2025-07-01',
      isExtra: false,
      Province: [
        {
          ontario: true,
          images: [
            {
              id: 'img1',
              name: 'A1-image.jpg',
              url: 'https://example.com/image1.jpg',
              alternativeText: 'Test image 1',
              hash: 'hash1',
              ext: 'jpg',
              mime: 'image/jpeg',
              size: 1000,
              provider: 'local',
              createdAt: '2025-01-01',
              updatedAt: '2025-01-01',
            },
          ],
          captionTexts: [
            {
              id: 'cap1',
              title: 'A1 August Long',
              postDate: '2025-08-04',
              text: 'Happy Civic Holiday!',
              backgroundInfo: 'Civic Holiday celebration',
            },
          ],
        },
      ],
      calendar: { url: 'https://example.com/calendar.pdf' },
      createdAt: '2025-01-01',
      updatedAt: '2025-01-01',
    };

    it('returns content for user province', () => {
      const result = getProvinceContent(mockPost, 'ontario');
      expect(result).toBeTruthy();
      expect(result?.images).toHaveLength(1);
      expect(result?.captions).toHaveLength(1);
    });

    it('returns null for unavailable province', () => {
      const result = getProvinceContent(mockPost, 'quebec');
      expect(result).toBeNull();
    });

    it('handles posts with all provinces access', () => {
      const allProvincesPost: SocialMediaPost = {
        ...mockPost,
        Province: [
          {
            all: true,
            images: mockPost.Province[0].images,
            captionTexts: mockPost.Province[0].captionTexts,
          },
        ],
      };
      const result = getProvinceContent(allProvincesPost, 'ontario');
      expect(result).toBeTruthy();
      expect(result?.images).toHaveLength(1);
    });

    it('processes captionTexts correctly', () => {
      const result = getProvinceContent(mockPost, 'ontario');
      expect(result).toBeTruthy();
      expect(result?.captions).toHaveLength(1);
      // The function processes captions from images when captionTexts is not available
      // It uses alternativeText from images as the caption text
      expect(result?.captions?.[0].text).toBe('Test image 1');
    });

    it('handles missing captionTexts', () => {
      const postWithoutCaptions: SocialMediaPost = {
        ...mockPost,
        Province: [
          {
            ontario: true,
            images: mockPost.Province[0].images,
            captionTexts: [],
          },
        ],
      };
      const result = getProvinceContent(postWithoutCaptions, 'ontario');
      expect(result).toBeTruthy();
      // When captionTexts is empty, the function processes captions from images
      expect(result?.captions).toBeTruthy();
    });

    it('handles missing images', () => {
      const postWithoutImages: SocialMediaPost = {
        ...mockPost,
        Province: [
          {
            ontario: true,
            images: [],
            captionTexts: mockPost.Province[0].captionTexts,
          },
        ],
      };
      const result = getProvinceContent(postWithoutImages, 'ontario');
      expect(result).toBeTruthy();
      expect(result?.images).toHaveLength(0);
    });
  });

  describe('createContentError', () => {
    it('creates error with default message', () => {
      const error = createContentError(new Error('Test error'));
      // The function returns the error message when it exists
      expect(error).toBe('Test error');
    });

    it('creates error with custom message', () => {
      const customError = new Error('Custom error message');
      const error = createContentError(customError);
      expect(error).toBe('Custom error message');
    });

    it('creates error with custom name', () => {
      const customError = new Error('Custom error');
      customError.name = 'CustomError';
      const error = createContentError(customError, 'ontario');
      expect(error).toBe('Custom error');
    });
  });
});
