import { socialMediaApi } from '../lib/api-client';
import { apiClient } from '@/shared/lib/api';

// Mock the shared API client
jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockGet = (apiClient.get as jest.MockedFunction<typeof apiClient.get>);

// Mock console.log to avoid noise in tests
const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

describe('Social Media API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('getLightweightPosts', () => {
    it('fetches lightweight posts successfully', async () => {
      const mockResponse = {
        success: true,
        data: {
          data: [
            {
              id: 1,
              documentId: 'doc1',
              month: '2025-07-01',
              isExtra: false,
              Province: [{ all: true }],
            },
          ],
        },
      };

      mockGet.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getLightweightPosts();

      expect(mockGet).toHaveBeenCalledWith('/social-medias/lightweight');
      expect(result.data).toEqual(mockResponse.data.data);
    });

    it('handles API errors gracefully', async () => {
      const errorMessage = 'Failed to fetch posts';
      mockGet.mockRejectedValue(new Error(errorMessage));

      await expect(socialMediaApi.getLightweightPosts()).rejects.toThrow(errorMessage);
      expect(mockGet).toHaveBeenCalledWith('/social-medias/lightweight');
    });

    it('handles empty response', async () => {
      const mockResponse = {
        success: true,
        data: { data: [] }
      };
      mockGet.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getLightweightPosts();

      expect(result.data).toHaveLength(0);
    });
  });

  describe('getSocialMediaPost', () => {
    it('fetches full post data successfully', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [
            {
              ontario: true,
              images: [
                {
                  id: 'img1',
                  name: 'A1-image.jpg',
                  url: 'https://example.com/image1.jpg',
                  alternativeText: 'Test image 1',
                  hash: 'hash1',
                  ext: 'jpg',
                  mime: 'image/jpeg',
                  size: 1000,
                  provider: 'local',
                  createdAt: '2025-01-01',
                  updatedAt: '2025-01-01',
                },
              ],
              captionTexts: [
                {
                  id: 'cap1',
                  title: 'A1 August Long',
                  postDate: '2025-08-04',
                  text: 'Happy Civic Holiday!',
                  backgroundInfo: 'Civic Holiday celebration',
                },
              ],
            },
          ],
          calendar: 'https://example.com/calendar.pdf',
        },
      };

      mockGet.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getSocialMediaPost(1);

      expect(mockGet).toHaveBeenCalledWith('/social-medias/1');
      expect(result).toEqual({ data: mockResponse.data });
    });

    it('handles API errors gracefully', async () => {
      const errorMessage = 'Failed to fetch post';
      mockGet.mockRejectedValue(new Error(errorMessage));

      await expect(socialMediaApi.getSocialMediaPost(1)).rejects.toThrow(errorMessage);
      expect(mockGet).toHaveBeenCalledWith('/social-medias/1');
    });

    it('handles different post IDs', async () => {
      const mockResponse = {
        success: true,
        data: { id: 123, documentId: 'doc123' }
      };
      mockGet.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getSocialMediaPost(123);

      expect(mockGet).toHaveBeenCalledWith('/social-medias/123');
      expect(result.data.id).toBe(123);
    });

    it('handles posts with missing data gracefully', async () => {
      const mockResponse = {
        success: true,
        data: {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [], // Empty Province array
        },
      };

      mockGet.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getSocialMediaPost(1);

      expect(result.data.Province).toHaveLength(0);
    });
  });

  describe('API endpoint consistency', () => {
    it('uses correct endpoint for lightweight posts', async () => {
      mockGet.mockResolvedValue({ success: true, data: { data: [] } });
      await socialMediaApi.getLightweightPosts();

      expect(mockGet).toHaveBeenCalledWith('/social-medias/lightweight');
    });

    it('uses correct endpoint for full post data', async () => {
      mockGet.mockResolvedValue({ success: true, data: {} });
      await socialMediaApi.getSocialMediaPost(456);

      expect(mockGet).toHaveBeenCalledWith('/social-medias/456');
    });
  });

  describe('Response handling', () => {
    it('preserves response structure', async () => {
      const mockResponse = {
        success: true,
        data: {
          data: [
            {
              id: 1,
              documentId: 'doc1',
              month: '2025-07-01',
              isExtra: false,
              Province: [{ all: true }],
            }
          ]
        },
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      mockGet.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getLightweightPosts();

      expect(result.data).toEqual(mockResponse.data.data);
      expect(result.data).toBeDefined();
    });

    it('handles malformed responses gracefully', async () => {
      const malformedResponse = { success: false, error: 'Invalid response' };
      mockGet.mockResolvedValue(malformedResponse);

      await expect(socialMediaApi.getLightweightPosts()).rejects.toThrow('Invalid response');
    });
  });

  describe('Error scenarios', () => {
    it('handles network errors', async () => {
      const networkError = new Error('Network Error');
      mockGet.mockRejectedValue(networkError);

      await expect(socialMediaApi.getLightweightPosts()).rejects.toThrow('Network Error');
    });

    it('handles HTTP error responses', async () => {
      const httpError = new Error('HTTP 404 Not Found');
      mockGet.mockRejectedValue(httpError);

      await expect(socialMediaApi.getSocialMediaPost(999)).rejects.toThrow('HTTP 404 Not Found');
    });

    it('handles timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      mockGet.mockRejectedValue(timeoutError);

      await expect(socialMediaApi.getLightweightPosts()).rejects.toThrow('Request timeout');
    });
  });

  describe('Data validation', () => {
    it('accepts valid post IDs', async () => {
      const validIds = [1, 123, 999999];

      for (const id of validIds) {
        mockGet.mockResolvedValue({ success: true, data: { id } });
        const result = await socialMediaApi.getSocialMediaPost(id);
        expect(result.data.id).toBe(id);
      }
    });

    it('handles edge case post IDs', async () => {
      const edgeCaseIds = [0, -1, Number.MAX_SAFE_INTEGER];

      for (const id of edgeCaseIds) {
        mockGet.mockResolvedValue({ success: true, data: { id } });
        const result = await socialMediaApi.getSocialMediaPost(id);
        expect(result.data.id).toBe(id);
      }
    });
  });
});
