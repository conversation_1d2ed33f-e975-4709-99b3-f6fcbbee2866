import { renderHook, waitFor } from '@testing-library/react';
import { useIndiApp } from '../hooks/use-indi-app';
import { apiClient } from '@/shared/lib/api';

// Mock the API client
jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('useIndiApp', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set up environment variable
    process.env.NEXT_PUBLIC_API_URL = 'http://localhost:1339';
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useIndiApp());

    // Initially loading
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(null);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useIndiApp());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should handle API error', async () => {
    const errorMessage = 'Network error';
    mockApiClient.get.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useIndiApp());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(errorMessage);
  });

  it('should handle empty response', async () => {
    mockApiClient.get.mockResolvedValue({ data: { data: [] }, status: 200, success: true });

    const { result } = renderHook(() => useIndiApp());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toBeTruthy();
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useIndiApp());

    // Should not throw when calling retry
    expect(() => result.current.retry()).not.toThrow();
  });
});
