"use client";

import React from "react";
import { Card, CardContent } from "@/shared/ui/card";
import { DocumentViewer } from "@/shared/components/document-viewer";
import { BodyTextRenderer } from "@/shared/components/body-text-renderer";
import type { Partnership } from "../types";

interface PartnershipLayoutProps {
  partnership: Partnership;
}

export function PartnershipLayout({ partnership }: PartnershipLayoutProps) {
  const getVideoEmbedUrl = (videoUrl: string): string => {
    // Handle Vimeo URLs
    if (videoUrl.includes('vimeo.com')) {
      const videoId = videoUrl.split('/').pop();
      return `https://player.vimeo.com/video/${videoId}`;
    }
    
    // Handle YouTube URLs
    if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
      let videoId = '';
      if (videoUrl.includes('youtube.com/watch?v=')) {
        videoId = videoUrl.split('v=')[1].split('&')[0];
      } else if (videoUrl.includes('youtu.be/')) {
        videoId = videoUrl.split('youtu.be/')[1].split('?')[0];
      }
      return `https://www.youtube.com/embed/${videoId}`;
    }
    
    // Return original URL if not recognized
    return videoUrl;
  };

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{partnership.Title}</h1>
      </div>

      {/* Banner Image */}
      {partnership.banner && (
        <div className="w-full">
          <img
            src={partnership.banner.url}
            alt={partnership.banner.alternativeText || partnership.Title}
            className="w-full h-auto rounded-lg shadow-sm"
            style={{ maxHeight: '400px', objectFit: 'cover' }}
          />
        </div>
      )}

      {/* Content Description */}
      {partnership.content && (
        <Card>
          <CardContent className="p-6">
            <div className="prose prose-gray max-w-none">
              <BodyTextRenderer content={partnership.content} />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Video Section */}
      {partnership.videoUrl && (
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">Video</h2>
            <div className="aspect-video w-full">
              <iframe
                src={getVideoEmbedUrl(partnership.videoUrl)}
                title={`${partnership.Title} Video`}
                className="w-full h-full rounded-lg"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* PDF Document Section */}
      {partnership.pdf && (
        <Card>
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4">Document</h2>
            <DocumentViewer
              fileUrl={partnership.pdf.url}
              title={partnership.pdf.name}
              fileExt={partnership.pdf.ext}
              fileName={partnership.pdf.name}
              fileMime={partnership.pdf.mime}
              backUrl="/partnerships"
              backLabel="Back to Partners"
            />
          </CardContent>
        </Card>
      )}

      {/* No additional content message */}
      {!partnership.content && !partnership.videoUrl && !partnership.pdf && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No additional content available for this partnership.</p>
        </div>
      )}
    </div>
  );
}
