export interface PartnershipBanner {
  id: number;
  name: string;
  alternativeText: string;
  caption: string;
  width: number;
  height: number;
  formats: {
    squared?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
    };
    thumbnail?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
    };
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
  documentId: string;
  locale: string | null;
  publishedAt: string;
  folderPath: string | null;
}

export interface PartnershipPDF {
  id: number;
  name: string;
  alternativeText: string;
  caption: string;
  width: number | null;
  height: number | null;
  formats: any[];
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
  documentId: string;
  locale: string | null;
  publishedAt: string;
  folderPath: string | null;
}

export interface Partnership {
  id: number;
  Title: string;
  slug: string;
  content: string;
  videoUrl?: string | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string | null;
  documentId: string;
  locale: string | null;
  banner?: PartnershipBanner | null;
  pdf?: PartnershipPDF | null;
}

export interface PartnershipsApiResponse {
  data: Partnership[];
  meta: Record<string, any>;
}

export interface PartnershipPageProps {
  params: Promise<{
    slug: string;
  }>;
}
