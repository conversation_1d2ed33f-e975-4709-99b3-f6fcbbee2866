import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import AwardsPage from '../components/awards-page';

// Mock the AwardsLayout component
jest.mock('@/shared/components', () => ({
  AwardsLayout: ({ children, data, isLoading, error, retryAction }: {
    children?: React.ReactNode;
    data: {
      pageTitle: string;
      description: string;
    };
    isLoading?: boolean;
    error?: string | null;
    retryAction?: () => void;
  }) => {
    if (isLoading) {
      return <div data-testid="loading">Loading...</div>;
    }
    if (error) {
      return (
        <div data-testid="error">
          <span>{error}</span>
          <button onClick={retryAction}>Try Again</button>
        </div>
      );
    }
    return (
      <div data-testid="awards-layout">
        <h1>{data.pageTitle}</h1>
        <div>{data.description}</div>
        {children}
      </div>
    );
  },
}));

const mockData = {
  id: '1',
  pageTitle: 'Awards',
  description: 'Recognition and awards for outstanding performance',
  prizesList: [
    {
      id: '1',
      title: 'Diamond Award',
      description: 'Highest tier award for top performers',
      amount: '50M+',
      thumbnail: {
        url: '/images/diamond-award.png',
        alt: 'Diamond Award'
      }
    }
  ],
  additionalAwardsList: [
    {
      id: '1',
      title: 'Customer Service Excellence',
      description: 'Award for outstanding customer service'
    }
  ]
};

describe('AwardsPage', () => {
  const defaultProps = {
    data: mockData,
    isLoading: false,
    error: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state correctly', () => {
    render(<AwardsPage {...defaultProps} isLoading={true} />);
    
    expect(screen.getByTestId('loading')).toBeInTheDocument();
    expect(screen.queryByText('Awards')).not.toBeInTheDocument();
  });

  it('renders error state correctly', () => {
    const mockRetry = jest.fn();
    render(
      <AwardsPage 
        {...defaultProps} 
        error="Failed to load data" 
        retryAction={mockRetry}
      />
    );
    
    expect(screen.getByTestId('error')).toBeInTheDocument();
    expect(screen.getByText('Failed to load data')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
    
    fireEvent.click(screen.getByText('Try Again'));
    expect(mockRetry).toHaveBeenCalledTimes(1);
  });

  it('renders page content correctly when data is loaded', () => {
    render(<AwardsPage {...defaultProps} />);
    
    expect(screen.getByText('Awards')).toBeInTheDocument();
    expect(screen.getByText('Recognition and awards for outstanding performance')).toBeInTheDocument();
    expect(screen.getByTestId('awards-layout')).toBeInTheDocument();
  });

  it('renders component without crashing', () => {
    const { container } = render(<AwardsPage {...defaultProps} />);
    expect(container).toBeInTheDocument();
  });

  it('handles missing data gracefully', () => {
    const emptyData = {
      id: '1',
      pageTitle: 'Awards',
      description: '',
      prizesList: [],
      additionalAwardsList: []
    };
    
    render(<AwardsPage {...defaultProps} data={emptyData} />);
    
    expect(screen.getByText('Awards')).toBeInTheDocument();
    expect(screen.getByTestId('awards-layout')).toBeInTheDocument();
  });
});
