import { z } from "zod";

// Phone number validation regex
const phoneRegex = /^[\d\s\-\(\)\+\.]+$/;

// URL validation helper
const urlSchema = z
  .string()
  .optional()
  .refine(
    (val) => {
      if (!val || val.trim() === "") return true;
      try {
        new URL(val.startsWith("http") ? val : `https://${val}`);
        return true;
      } catch {
        return false;
      }
    },
    { message: "Please enter a valid URL" }
  );

export const realtorFormSchema = z.object({
  firstname: z
    .string()
    .min(1, "First name is required")
    .max(50, "First name must be less than 50 characters"),
  
  middlename: z
    .string()
    .max(50, "Middle name must be less than 50 characters")
    .optional(),
  
  lastname: z
    .string()
    .min(1, "Last name is required")
    .max(50, "Last name must be less than 50 characters"),
  
  position: z
    .string()
    .max(100, "Position must be less than 100 characters")
    .optional(),
  
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  
  phone: z
    .string()
    .min(1, "Phone number is required")
    .regex(phoneRegex, "Please enter a valid phone number"),
  
  company: z
    .string()
    .max(100, "Company name must be less than 100 characters")
    .optional(),
  
  website: urlSchema,
  
  useDefaultPhoto: z.boolean(),
});

export type RealtorFormData = z.infer<typeof realtorFormSchema>;
