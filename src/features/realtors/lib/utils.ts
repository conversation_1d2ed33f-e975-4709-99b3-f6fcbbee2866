/**
 * Format URL to ensure it has a protocol
 */
export function formatUrl(url: string): string {
  if (!url) return "";
  
  // Remove any whitespace
  const cleanUrl = url.trim();
  
  // If it already has a protocol, return as is
  if (cleanUrl.match(/^https?:\/\//)) {
    return cleanUrl;
  }
  
  // Add https:// if no protocol is present
  return `https://${cleanUrl}`;
}

/**
 * Format phone number by removing non-digit characters
 */
export function formatPhoneNumber(phone: string): string {
  if (!phone) return "";
  return phone.replace(/\D/g, "");
}

/**
 * Get raw phone number data for API submission
 */
export function getRawPhone(formData: any): Record<string, any> {
  const phones: Record<string, any> = {};
  
  if (formData.phone) {
    phones.phone = formatPhoneNumber(formData.phone);
  }
  
  return phones;
}

/**
 * Validate file size and type for photo upload
 */
export function validatePhotoFile(file: File): { isValid: boolean; error?: string } {
  const maxSize = 15 * 1024 * 1024; // 15MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'The image file exceeds the maximum size of 15MB.'
    };
  }
  
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Only JPG and PNG files are allowed.'
    };
  }
  
  return { isValid: true };
}

/**
 * Create a preview URL for uploaded file
 */
export function createFilePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Clean up preview URL
 */
export function revokeFilePreview(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Transform Strapi V5 realtor data to internal Realtor format
 */
export function transformStrapiRealtor(strapiRealtor: any): any {
  return {
    id: strapiRealtor.id.toString(),
    firstname: strapiRealtor.firstname || '',
    middlename: strapiRealtor.middlename || undefined,
    lastname: strapiRealtor.lastname || '',
    position: strapiRealtor.position || undefined,
    email: strapiRealtor.email || undefined,
    phone: strapiRealtor.phone || undefined,
    company: strapiRealtor.company || undefined,
    website: strapiRealtor.website || undefined,
    createdAt: strapiRealtor.createdAt,
    updatedAt: strapiRealtor.updatedAt,
    documentId: strapiRealtor.documentId,
    photo: strapiRealtor.photo ? {
      id: strapiRealtor.photo.id.toString(),
      url: strapiRealtor.photo.url,
      formats: strapiRealtor.photo.formats,
    } : undefined,
  };
}
