"use client";

import { useState, useCallback, useEffect } from "react";
import { getCookie } from "@/shared/lib/auth";
import { formatUrl, getRawPhone, validatePhotoFile, transformStrapiRealtor } from "../lib/utils";
import { showSuccess, showError } from "@/shared/lib/toast";
import type { Realtor, RealtorFormData, ProcessingState, FileUploadState, StrapiRealtorsResponse, StrapiRealtorResponse, UpdateRealtorData } from "../types";

export function useRealtors(initialRealtors: Realtor[] = []) {
  const [realtors, setRealtors] = useState<Realtor[]>(initialRealtors);
  const [processing, setProcessing] = useState<ProcessingState>({
    isVisible: false,
    status: 'loading',
    message: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  const fetchRealtors = useCallback(async () => {
    setIsLoading(true);
    try {
      const userId = getCookie('userId');
      const token = getCookie('jwt');

      if (!userId || !token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/realtors?filters[user][$eq]=${userId}&populate=*`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch realtors');
      }

      const data: StrapiRealtorsResponse = await response.json();
      
      // Transform Strapi response to internal format
      const transformedRealtors = data.data.map(transformStrapiRealtor);
      setRealtors(transformedRealtors);
      
      return transformedRealtors;
    } catch (error) {
      console.error('Error fetching realtors:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchRealtor = useCallback(async (documentId: string): Promise<Realtor> => {
    try {
      const token = getCookie('jwt');

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/realtors/${documentId}?populate=*`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch realtor');
      }

      const data: StrapiRealtorResponse = await response.json();
      return transformStrapiRealtor(data.data);
    } catch (error) {
      console.error('Error fetching realtor:', error);
      throw error;
    }
  }, []);

  const updateRealtor = useCallback(async (
    documentId: string,
    formData: RealtorFormData,
    photoFile?: File | null,
    useDefaultPhoto?: boolean,
    existingRealtor?: Realtor
  ): Promise<Realtor> => {
    setProcessing({
      isVisible: true,
      status: 'loading',
      message: 'Updating realtor...',
    });

    try {
      const token = getCookie('jwt');

      if (!token) {
        throw new Error('Authentication required');
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`
        }
      };

      let uploadedPhoto = null;

      // Handle photo upload if there's a new photo
      if (useDefaultPhoto) {
        // Use default photo
        const defaultPhotoUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/documents/listing-sheet/full/realtor-image-default.jpg`;
        const buffer = await fetch(defaultPhotoUrl).then(res => res.arrayBuffer());
        const bytes = new Uint8Array(buffer);
        const photoBlob = new Blob([bytes], { type: 'image/jpg' });
        
        const photoData = new FormData();
        photoData.append('files', photoBlob, 'A-logo.jpg');
        photoData.append('field', 'photo');

        const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload`, {
          method: 'POST',
          headers: config.headers,
          body: photoData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload default photo');
        }

        const uploadData = await uploadResponse.json();
        uploadedPhoto = uploadData[0];
      } else if (photoFile) {
        // Validate photo file
        const validation = validatePhotoFile(photoFile);
        if (!validation.isValid) {
          throw new Error(validation.error);
        }

        const photoData = new FormData();
        const ext = photoFile.name.split('.').pop();
        const filename = `${formData.firstname} ${formData.lastname}.${ext}`;
        photoData.append('files', photoFile, filename);
        photoData.append('field', 'photo');

        const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload`, {
          method: 'POST',
          headers: config.headers,
          body: photoData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload photo');
        }

        const uploadData = await uploadResponse.json();
        uploadedPhoto = uploadData[0];
      }

      // Prepare update data
      const rawPhones = getRawPhone(formData);
      const updateData: UpdateRealtorData = {
        data: {
          ...formData,
          ...rawPhones,
          website: formData.website ? formatUrl(formData.website) : undefined,
        }
      };

      // Add photo if there's a new one, otherwise preserve existing photo
      if (uploadedPhoto) {
        updateData.data.photo = uploadedPhoto?.id;
      } else if (existingRealtor?.photo?.id) {
        // Preserve existing photo ID if no new photo is uploaded
        updateData.data.photo = existingRealtor.photo.id;
      }

      // Remove useDefaultPhoto from the data before sending to API
      const { useDefaultPhoto: _, ...realtorUpdateData } = updateData.data;

      // Update realtor
      const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/realtors/${documentId}`, {
        method: 'PUT',
        headers: {
          ...config.headers,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: realtorUpdateData }),
      });

      if (!updateResponse.ok) {
        throw new Error('Failed to update realtor');
      }

      const updatedRealtor = await updateResponse.json();

      setProcessing({
        isVisible: false,
        status: 'success',
        message: 'Realtor updated successfully!',
      });

      showSuccess('Realtor Updated', 'The realtor has been updated successfully!');

      // Refresh the realtors list
      await fetchRealtors();

      return transformStrapiRealtor(updatedRealtor.data);
    } catch (error) {
      console.error('Error updating realtor:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setProcessing({
        isVisible: false,
        status: 'error',
        message: errorMessage,
      });
      showError('Failed to Update Realtor', errorMessage);
      throw error;
    }
  }, [fetchRealtors]);

  // Fetch realtors on mount if no initial data
  useEffect(() => {
    if (initialRealtors.length === 0) {
      fetchRealtors();
    }
  }, [fetchRealtors, initialRealtors.length]);

  const addRealtor = useCallback(async (
    formData: RealtorFormData,
    photoFile?: File | null,
    useDefaultPhoto?: boolean
  ) => {
    setProcessing({
      isVisible: true,
      status: 'loading',
      message: 'Uploading Photo...',
    });

    try {
      const userId = getCookie('userId');
      const token = getCookie('jwt');

      if (!userId || !token) {
        throw new Error('Authentication required');
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`
        }
      };

      let uploadedPhoto = null;

      // Handle photo upload
      if (useDefaultPhoto) {
        // Use default photo
        const defaultPhotoUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/documents/listing-sheet/full/realtor-image-default.jpg`;
        const buffer = await fetch(defaultPhotoUrl).then(res => res.arrayBuffer());
        const bytes = new Uint8Array(buffer);
        const photoBlob = new Blob([bytes], { type: 'image/jpg' });
        
        const photoData = new FormData();
        photoData.append('files', photoBlob, 'A-logo.jpg');
        photoData.append('field', 'photo');

        const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload`, {
          method: 'POST',
          headers: config.headers,
          body: photoData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload default photo');
        }

        const uploadData = await uploadResponse.json();
        uploadedPhoto = uploadData[0];
      } else if (photoFile) {
        // Validate photo file
        const validation = validatePhotoFile(photoFile);
        if (!validation.isValid) {
          throw new Error(validation.error);
        }

        const photoData = new FormData();
        const ext = photoFile.name.split('.').pop();
        const filename = `${formData.firstname} ${formData.lastname}.${ext}`;
        photoData.append('files', photoFile, filename);
        photoData.append('field', 'photo');

        const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload`, {
          method: 'POST',
          headers: config.headers,
          body: photoData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload photo');
        }

        const uploadData = await uploadResponse.json();
        uploadedPhoto = uploadData[0];
      } else {
        // No photo uploaded and no default photo selected - use default photo as fallback
        const defaultPhotoUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/documents/listing-sheet/full/realtor-image-default.jpg`;
        const buffer = await fetch(defaultPhotoUrl).then(res => res.arrayBuffer());
        const bytes = new Uint8Array(buffer);
        const photoBlob = new Blob([bytes], { type: 'image/jpg' });
        
        const photoData = new FormData();
        photoData.append('files', photoBlob, 'A-logo.jpg');
        photoData.append('field', 'photo');

        const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload`, {
          method: 'POST',
          headers: config.headers,
          body: photoData,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload default photo');
        }

        const uploadData = await uploadResponse.json();
        uploadedPhoto = uploadData[0];
      }

      setProcessing({
        isVisible: true,
        status: 'loading',
        message: 'Saving realtor info...',
      });

      // Prepare realtor data
      const rawPhones = getRawPhone(formData);
      const realtorData = {
        ...formData,
        ...rawPhones,
        website: formData.website ? formatUrl(formData.website) : undefined,
        photo: uploadedPhoto.id,
        user: userId, // Include the user ID for the relational field
      };
      
      // Remove useDefaultPhoto from the data before sending to API
      const { useDefaultPhoto: _, ...cleanRealtorData } = realtorData;

      // Create realtor
      const realtorResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/realtors`, {
        method: 'POST',
        headers: {
          ...config.headers,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: cleanRealtorData }),
      });

      if (!realtorResponse.ok) {
        throw new Error('Failed to create realtor');
      }

      const savedRealtor = await realtorResponse.json();

      setProcessing({
        isVisible: false,
        status: 'success',
        message: 'Realtor added!',
      });

      showSuccess('Realtor Added', 'The realtor has been added successfully!');

      // Refresh realtors list to get the updated data
      await fetchRealtors();

      return savedRealtor;
    } catch (error) {
      console.error('Error adding realtor:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setProcessing({
        isVisible: false,
        status: 'error',
        message: errorMessage,
      });
      showError('Failed to Add Realtor', errorMessage);
      throw error;
    }
  }, [realtors]);

  const deleteRealtor = useCallback(async (realtorId: string) => {
    setProcessing({
      isVisible: true,
      status: 'loading',
      message: 'Deleting realtor...',
    });

    try {
      const userId = getCookie('userId');
      const token = getCookie('jwt');

      if (!userId || !token) {
        throw new Error('Authentication required');
      }

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      };

      // Remove realtor from array
      const updatedRealtors = realtors.filter(realtor => realtor.id !== realtorId);
      
      const userUpdateResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${userId}`, {
        method: 'PUT',
        headers: config.headers,
        body: JSON.stringify({ realtors: updatedRealtors }),
      });

      if (!userUpdateResponse.ok) {
        throw new Error('Failed to delete realtor');
      }

      // Refresh realtors list to get the updated data
      await fetchRealtors();

      setProcessing({
        isVisible: false,
        status: 'success',
        message: 'Realtor deleted!',
      });

      showSuccess('Realtor Deleted', 'The realtor has been deleted successfully!');
    } catch (error) {
      console.error('Error deleting realtor:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setProcessing({
        isVisible: false,
        status: 'error',
        message: errorMessage,
      });
      showError('Failed to Delete Realtor', errorMessage);
      throw error;
    }
  }, [realtors]);

  return {
    realtors,
    processing,
    isLoading,
    fetchRealtors,
    fetchRealtor,
    updateRealtor,
    addRealtor,
    deleteRealtor,
  };
}
