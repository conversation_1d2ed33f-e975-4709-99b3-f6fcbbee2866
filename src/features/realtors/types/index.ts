export interface Realtor {
  id: string;
  firstname: string;
  middlename?: string;
  lastname: string;
  position?: string;
  email?: string;
  phone?: string;
  company?: string;
  website?: string;
  photo?: {
    id: string;
    url: string;
    formats?: {
      thumbnail?: { url: string };
      small?: { url: string };
      medium?: { url: string };
    };
    [key: string]: any;
  };
  createdAt?: string;
  updatedAt?: string;
  documentId?: string;
}

// Strapi V5 API response types
export interface StrapiRealtor {
  id: number;
  firstname: string | null;
  middlename: string | null;
  lastname: string | null;
  position: string | null;
  company: string | null;
  email: string | null;
  phone: string | null;
  website: string | null;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  documentId: string;
  useDefaultPhoto: boolean;
  photo?: {
    id: number;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: {
      small?: { url: string; [key: string]: any };
      medium?: { url: string; [key: string]: any };
      squared?: { url: string; [key: string]: any };
      thumbnail?: { url: string; [key: string]: any };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: any;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    publishedAt: string;
  };
}

export interface StrapiRealtorsResponse {
  data: StrapiRealtor[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface StrapiRealtorResponse {
  data: StrapiRealtor;
  meta: {};
}

export interface UpdateRealtorData {
  data: {
    firstname?: string;
    middlename?: string;
    lastname?: string;
    position?: string;
    email?: string;
    phone?: string;
    company?: string;
    website?: string;
    photo?: any;
    useDefaultPhoto?: boolean;
  };
}

export interface RealtorFormData {
  firstname: string;
  middlename?: string;
  lastname: string;
  position?: string;
  email?: string;
  phone?: string;
  company?: string;
  website?: string;
  useDefaultPhoto?: boolean;
}

export interface User {
  id: string;
  firstname?: string;
  lastname?: string;
  realtors?: Realtor[];
  [key: string]: any;
}

export interface FileUploadState {
  file: File | null;
  preview: string | null;
  isUploading: boolean;
  error: string | null;
  useDefaultPhoto?: boolean;
}

export interface ProcessingState {
  isVisible: boolean;
  status: 'success' | 'error' | 'loading';
  message: string;
}
