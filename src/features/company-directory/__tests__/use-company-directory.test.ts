import { renderHook } from '@testing-library/react';
import { useCompanyDirectory } from '../hooks/use-company-directory';

jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

describe('useCompanyDirectory', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useCompanyDirectory());

    expect(result.current.isLoading).toBe(true);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useCompanyDirectory());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useCompanyDirectory());

    expect(() => result.current.retry()).not.toThrow();
  });
});
