"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getAuthHeaders } from "@/shared/lib/auth";
import type { 
  UserData, 
  WebsiteData, 
  IndiSitesFormData,
  WebsiteInfoFormData,
  SocialLinksFormData,
  HtmlTagsFormData
} from "@/features/indi-sites/types";

export function useIndiSites() {
  const [user, setUser] = useState<UserData | null>(null);
  const [website, setWebsite] = useState<WebsiteData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userAuth } = useAuthContext();

  useEffect(() => {
    async function fetchIndiSitesData() {
      if (!userAuth.isAuth || !userAuth.initialized || !userAuth.userInfo) {
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Set user data from auth context
        setUser(userAuth.userInfo as UserData);

        // Fetch website data
        const websiteResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/websites?filters[user]=${userAuth.userInfo.id}`, {
          headers: getAuthHeaders(),
          cache: 'no-store',
        });

        if (websiteResponse.ok) {
          const websiteData = await websiteResponse.json();
          // Strapi V5 returns data in an array, use the first item
          setWebsite(websiteData.data?.[0] || null);
        } else {
          setWebsite(null);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        console.error('Error fetching indi sites data:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchIndiSitesData();
  }, [userAuth.isAuth, userAuth.initialized, userAuth.userInfo]);

  // Update website info tab (user data only)
  const updateWebsiteInfo = useCallback(async (data: WebsiteInfoFormData): Promise<void> => {
    if (!userAuth.userInfo) {
      throw new Error('User not authenticated');
    }

    const config = {
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json',
      },
    };

    try {
      // Prepare user data (excluding HTML tags)
      const userUpdateData = {
        firstname: data.firstname,
        middlename: data.middlename,
        lastname: data.lastname,
        titles: data.titles,
        position: data.position,
        license: data.license,
        workEmail: data.workEmail,
        phone: data.phone,
      };

      // Update user data
      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${userAuth.userInfo.id}`, {
        method: 'PUT',
        ...config,
        body: JSON.stringify(userUpdateData),
      });

      if (!userResponse.ok) {
        throw new Error('Failed to update user data');
      }

      // Update user data in local state
      const updatedUser = await userResponse.json();
      setUser(updatedUser);

    } catch (error) {
      console.error('Error updating website info:', error);
      throw error;
    }
  }, [userAuth.userInfo]);

  // Update social links tab (user data only)
  const updateSocialLinks = useCallback(async (data: SocialLinksFormData): Promise<void> => {
    if (!userAuth.userInfo) {
      throw new Error('User not authenticated');
    }

    const config = {
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json',
      },
    };

    try {
      // Prepare user data (social links only)
      const userUpdateData = {
        instagram: data.instagram,
        facebook: data.facebook,
        linkedin: data.linkedin,
        twitter: data.twitter,
        youtube: data.youtube,
        applicationLink: data.applicationLink,
        appointmentScheduleLink: data.appointmentScheduleLink,
      };

      // Update user data
      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${userAuth.userInfo.id}`, {
        method: 'PUT',
        ...config,
        body: JSON.stringify(userUpdateData),
      });

      if (!userResponse.ok) {
        throw new Error('Failed to update user data');
      }

      // Update user data in local state
      const updatedUser = await userResponse.json();
      setUser(updatedUser);

    } catch (error) {
      console.error('Error updating social links:', error);
      throw error;
    }
  }, [userAuth.userInfo]);

  // Update HTML tags tab (website data only)
  const updateHtmlTags = useCallback(async (data: HtmlTagsFormData): Promise<void> => {
    if (!userAuth.userInfo) {
      throw new Error('User not authenticated');
    }

    const config = {
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json',
      },
    };

    try {
      // Prepare user data (HTML tags fields for backward compatibility)
      const userUpdateData = {
        chatWidgetCode: data.chatWidgetCode,
        reviewWidgetCode: data.reviewWidgetCode,
        googleTag: data.googleTag,
        googleTagManagerInHead: data.googleTagManagerInHead,
        googleTagManagerInBody: data.googleTagManagerInBody,
        googleWebsiteVerification: data.googleWebsiteVerification,
        facebookPixelTag: data.facebookPixelTag,
        thirdPartyScriptTag: data.thirdPartyScriptTag,
      };

      // Prepare website data (HTML tags only)
      const websiteData = {
        data: {
          chatWidgetCode: data.chatWidgetCode,
          reviewWidgetCode: data.reviewWidgetCode,
          googleTag: data.googleTag,
          googleTagManagerInHead: data.googleTagManagerInHead,
          googleTagManagerInBody: data.googleTagManagerInBody,
          googleWebsiteVerification: data.googleWebsiteVerification,
          facebookPixelTag: data.facebookPixelTag,
          thirdPartyScriptTag: data.thirdPartyScriptTag,          
        }
      };

      // Update user data first (for backward compatibility)
      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${userAuth.userInfo.id}`, {
        method: 'PUT',
        ...config,
        body: JSON.stringify(userUpdateData),
      });

      if (!userResponse.ok) {
        throw new Error('Failed to update user data');
      }

      // Update user data in local state
      const updatedUser = await userResponse.json();
      setUser(updatedUser);

      // Check if website exists
      const existingWebsiteResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/websites?filters[user]=${userAuth.userInfo.id}`, {
        headers: config.headers,
      });

      if (existingWebsiteResponse.ok) {
        // Update existing website
        const existingWebsiteData = await existingWebsiteResponse.json();
        const existingWebsite = existingWebsiteData.data?.[0]; // Get first website from array
        
        if (existingWebsite) {
          const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/websites/${existingWebsite.id}`, {
            method: 'PUT',
            ...config,
            body: JSON.stringify(websiteData),
          });

          if (updateResponse.ok) {
            const updatedWebsite = await updateResponse.json();
            setWebsite(updatedWebsite);
          }
        }
      } else {
        // Create new website
        const createResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/websites`, {
          method: 'POST',
          ...config,
          body: JSON.stringify(websiteData),
        });

        if (createResponse.ok) {
          const newWebsite = await createResponse.json();
          setWebsite(newWebsite);
        }
      }

    } catch (error) {
      console.error('Error updating HTML tags:', error);
      throw error;
    }
  }, [userAuth.userInfo]);

  const updateIndiSites = useCallback(async (data: IndiSitesFormData): Promise<void> => {
    if (!userAuth.userInfo) {
      throw new Error('User not authenticated');
    }

    const config = {
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json',
      },
    };

    try {
      // Prepare user data (excluding HTML tags)
      const userUpdateData = {
        firstname: data.firstname,
        middlename: data.middlename,
        lastname: data.lastname,
        titles: data.titles,
        position: data.position,
        license: data.license,
        workEmail: data.workEmail,
        phone: data.phone,
        instagram: data.instagram,
        facebook: data.facebook,
        linkedin: data.linkedin,
        twitter: data.twitter,
        youtube: data.youtube,
        applicationLink: data.applicationLink,
        appointmentScheduleLink: data.appointmentScheduleLink,
        // HTML Tags fields for backward compatibility
        chatWidgetCode: data.chatWidgetCode,
        reviewWidgetCode: data.reviewWidgetCode,
        googleTag: data.googleTag,
        googleTagManagerInHead: data.googleTagManagerInHead,
        googleTagManagerInBody: data.googleTagManagerInBody,
        googleWebsiteVerification: data.googleWebsiteVerification,
        facebookPixelTag: data.facebookPixelTag,
        thirdPartyScriptTag: data.thirdPartyScriptTag,
      };

      // Prepare website data (HTML tags only)
      const websiteData = {
        data: {
          chatWidgetCode: data.chatWidgetCode,
          reviewWidgetCode: data.reviewWidgetCode,
          googleTag: data.googleTag,
          googleTagManagerInHead: data.googleTagManagerInHead,
          googleTagManagerInBody: data.googleTagManagerInBody,
          googleWebsiteVerification: data.googleWebsiteVerification,
          facebookPixelTag: data.facebookPixelTag,
          thirdPartyScriptTag: data.thirdPartyScriptTag,
          userId: userAuth.userInfo.id,
        }
      };

      // Update user data
      const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${userAuth.userInfo.id}`, {
        method: 'PUT',
        ...config,
        body: JSON.stringify(userUpdateData),
      });

      if (!userResponse.ok) {
        throw new Error('Failed to update user data');
      }

      // Check if website exists
      const existingWebsiteResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/websites?filters[user]=${userAuth.userInfo.id}`, {
        headers: config.headers,
      });

      if (existingWebsiteResponse.ok) {
        // Update existing website
        const existingWebsiteData = await existingWebsiteResponse.json();
        const existingWebsite = existingWebsiteData.data?.[0]; // Get first website from array
        
        if (existingWebsite) {
          const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/websites/${existingWebsite.id}`, {
            method: 'PUT',
            ...config,
            body: JSON.stringify(websiteData),
          });

          if (updateResponse.ok) {
            const updatedWebsite = await updateResponse.json();
            setWebsite(updatedWebsite);
          }
        }
      } else {
        // Create new website
        const createResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/websites`, {
          method: 'POST',
          ...config,
          body: JSON.stringify(websiteData),
        });

        if (createResponse.ok) {
          const newWebsite = await createResponse.json();
          setWebsite(newWebsite);
        }
      }

      // Update user data in local state
      const updatedUser = await userResponse.json();
      setUser(updatedUser);

    } catch (error) {
      console.error('Error updating indi sites data:', error);
      throw error;
    }
  }, [userAuth.userInfo]);

  return {
    user,
    website,
    isLoading,
    error,
    updateIndiSites,
    updateWebsiteInfo,
    updateSocialLinks,
    updateHtmlTags,
  };
}
