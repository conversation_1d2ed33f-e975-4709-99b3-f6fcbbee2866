export interface WebsiteData {
  id?: string;
  chatWidgetCode?: string;
  reviewWidgetCode?: string;
  googleTag?: string;
  googleTagManagerInHead?: string;
  googleTagManagerInBody?: string;
  googleWebsiteVerification?: string;
  facebookPixelTag?: string;
  thirdPartyScriptTag?: string;
  userId?: string;
}

export interface UserData {
  id: string;
  firstname?: string;
  middlename?: string;
  lastname?: string;
  titles?: string;
  position?: string;
  license?: string;
  workEmail?: string;
  phone?: string;
  instagram?: string;
  facebook?: string;
  linkedin?: string;
  twitter?: string;
  youtube?: string;
  applicationLink?: string;
  appointmentScheduleLink?: string;
  // HTML Tags fields for backward compatibility
  chatWidgetCode?: string;
  reviewWidgetCode?: string;
  googleTag?: string;
  googleTagManagerInHead?: string;
  googleTagManagerInBody?: string;
  googleWebsiteVerification?: string;
  facebookPixelTag?: string;
  thirdPartyScriptTag?: string;
  photo?: {
    url?: string;
    hash?: string;
    ext?: string;
    [key: string]: any;
  };
}

export interface IndiSitesFormData {
  // Personal Information
  firstname: string;
  middlename?: string;
  lastname: string;
  titles?: string;
  position?: string;
  license?: string;
  workEmail?: string;
  phone: string;
  
  // Social Media
  instagram?: string;
  facebook?: string;
  linkedin?: string;
  twitter?: string;
  youtube?: string;
  
  // Business Links
  applicationLink?: string;
  appointmentScheduleLink?: string;
  
  // HTML Tags
  chatWidgetCode?: string;
  reviewWidgetCode?: string;
  googleTag?: string;
  googleTagManagerInHead?: string;
  googleTagManagerInBody?: string;
  googleWebsiteVerification?: string;
  facebookPixelTag?: string;
  thirdPartyScriptTag?: string;
}

// Tab-specific form data types
export interface WebsiteInfoFormData {
  firstname: string;
  middlename?: string;
  lastname: string;
  titles?: string;
  position?: string;
  license?: string;
  workEmail?: string;
  phone: string;
}

export interface SocialLinksFormData {
  instagram?: string;
  facebook?: string;
  linkedin?: string;
  twitter?: string;
  youtube?: string;
  applicationLink?: string;
  appointmentScheduleLink?: string;
}

export interface HtmlTagsFormData {
  chatWidgetCode?: string;
  reviewWidgetCode?: string;
  googleTag?: string;
  googleTagManagerInHead?: string;
  googleTagManagerInBody?: string;
  googleWebsiteVerification?: string;
  facebookPixelTag?: string;
  thirdPartyScriptTag?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}
