"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Loader2, Save } from "lucide-react";
import { websiteInfoSchema, type WebsiteInfoFormData } from "../lib/validation";
import { showSuccess, showError } from "@/shared/lib/toast";
import type { UserData } from "../types";

interface WebsiteInfoFormProps {
  user: UserData;
  onSubmit: (data: WebsiteInfoFormData) => Promise<void>;
}

export function WebsiteInfoForm({ user, onSubmit }: WebsiteInfoFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<WebsiteInfoFormData>({
    resolver: zodResolver(websiteInfoSchema),
    defaultValues: {
      firstname: user.firstname || "",
      middlename: user.middlename || "",
      lastname: user.lastname || "",
      titles: user.titles || "",
      position: user.position || "",
      license: user.license || "",
      workEmail: user.workEmail || "",
      phone: user.phone || "",
    },
  });

  const handleFormSubmit = async (data: WebsiteInfoFormData) => {
    setIsSubmitting(true);

    try {
      await onSubmit(data);
      showSuccess("Website Info Updated", "Your website info has been updated successfully!");
    } catch (error) {
      console.error("Form submission error:", error);
      showError("Update Failed", error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Website Info</CardTitle>
            <div className="border-blue-200 bg-blue-50 p-4 rounded-lg">
              <p className="text-blue-800 text-sm">
                <strong>Warning: You must use the name you have registered with the provincial regulator.</strong>
                <br />
                Any data modified in this section will be updated on your Website and on your Indi Central Profile as well.
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Name Fields */}
              <div className="space-y-2">
                <Label htmlFor="firstname">
                  First Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="firstname"
                  {...register("firstname")}
                  className={errors.firstname ? "border-red-500" : ""}
                />
                {errors.firstname && (
                  <p className="text-sm text-red-500">{errors.firstname.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="middlename">Middle Name</Label>
                <Input id="middlename" {...register("middlename")} />
                {errors.middlename && (
                  <p className="text-sm text-red-500">{errors.middlename.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastname">
                  Last Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="lastname"
                  {...register("lastname")}
                  className={errors.lastname ? "border-red-500" : ""}
                />
                {errors.lastname && (
                  <p className="text-sm text-red-500">{errors.lastname.message}</p>
                )}
              </div>

              {/* Professional Info */}
              <div className="space-y-2">
                <Label htmlFor="titles">Title After Name (e.g. AMP, BCC)</Label>
                <Input
                  id="titles"
                  placeholder="AMP, BCC, BCO"
                  {...register("titles")}
                />
                {errors.titles && (
                  <p className="text-sm text-red-500">{errors.titles.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Input
                  id="position"
                  placeholder="I.E: Mortgage Broker, BCS"
                  {...register("position")}
                />
                {errors.position && (
                  <p className="text-sm text-red-500">{errors.position.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="license">License Number (Optional)</Label>
                <Input
                  id="license"
                  placeholder="I.E: #AXM003333"
                  {...register("license")}
                />
                {errors.license && (
                  <p className="text-sm text-red-500">{errors.license.message}</p>
                )}
              </div>

              {/* Contact Info */}
              <div className="space-y-2">
                <Label htmlFor="workEmail">Preferred Email Address</Label>
                <Input
                  id="workEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("workEmail")}
                />
                {errors.workEmail && (
                  <p className="text-sm text-red-500">{errors.workEmail.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">
                  Preferred Phone Number <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="************"
                  {...register("phone")}
                  className={errors.phone ? "border-red-500" : ""}
                />
                {errors.phone && (
                  <p className="text-sm text-red-500">{errors.phone.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting} className="min-w-32">
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Info
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
