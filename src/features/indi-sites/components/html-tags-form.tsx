"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/shared/ui/button";
import { Textarea } from "@/shared/ui/textarea";
import { Label } from "@/shared/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Loader2, Save } from "lucide-react";
import { htmlTagsSchema, type HtmlTagsFormData } from "../lib/validation";
import { showSuccess, showError } from "@/shared/lib/toast";
import type { WebsiteData, UserData } from "../types";

interface HtmlTagsFormProps {
  user?: UserData | null;
  website?: WebsiteData | null;
  onSubmit: (data: HtmlTagsFormData) => Promise<void>;
}

export function HtmlTagsForm({ user, website, onSubmit }: HtmlTagsFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<HtmlTagsFormData>({
    resolver: zodResolver(htmlTagsSchema),
    defaultValues: {
      chatWidgetCode: website?.chatWidgetCode || user?.chatWidgetCode || "",
      reviewWidgetCode: website?.reviewWidgetCode || user?.reviewWidgetCode || "",
      googleTag: website?.googleTag || user?.googleTag || "",
      googleTagManagerInHead: website?.googleTagManagerInHead || user?.googleTagManagerInHead || "",
      googleTagManagerInBody: website?.googleTagManagerInBody || user?.googleTagManagerInBody || "",
      googleWebsiteVerification: website?.googleWebsiteVerification || user?.googleWebsiteVerification || "",
      facebookPixelTag: website?.facebookPixelTag || user?.facebookPixelTag || "",
      thirdPartyScriptTag: website?.thirdPartyScriptTag || user?.thirdPartyScriptTag || "",
    },
  });

  const handleFormSubmit = async (data: HtmlTagsFormData) => {
    setIsSubmitting(true);

    try {
      await onSubmit(data);
      showSuccess("HTML Tags Updated", "Your HTML tags have been updated successfully!");
    } catch (error) {
      console.error("Form submission error:", error);
      showError("Update Failed", error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>HTML Tags</CardTitle>
            <p className="text-sm text-muted-foreground">
              Insert below your website HTML tags, for example: Google Tag, Google Tracking, Google Analytics, Facebook Pixel.
            </p>
            <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
              <li>Insert only the tag, no additional info.</li>
              <li>If you need to add any HTML tag from a different provider than the ones listed below, please get in touch with support.</li>
            </ul>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="chatWidgetCode">Chat Widget Code (Indi App)</Label>
                <Textarea
                  id="chatWidgetCode"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("chatWidgetCode")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reviewWidgetCode">Reviews Widget Code (Indi App)</Label>
                <Textarea
                  id="reviewWidgetCode"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("reviewWidgetCode")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="googleTag">Google Tag</Label>
                <Textarea
                  id="googleTag"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("googleTag")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="googleTagManagerInHead">Google Tag Manager in &lt;head&gt;</Label>
                <Textarea
                  id="googleTagManagerInHead"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("googleTagManagerInHead")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="googleTagManagerInBody">Google Tag Manager in &lt;body&gt;</Label>
                <Textarea
                  id="googleTagManagerInBody"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("googleTagManagerInBody")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="googleWebsiteVerification">Google Website Verification &lt;meta&gt;</Label>
                <Textarea
                  id="googleWebsiteVerification"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("googleWebsiteVerification")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="facebookPixelTag">Facebook Pixel</Label>
                <Textarea
                  id="facebookPixelTag"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("facebookPixelTag")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="thirdPartyScriptTag">Other Provider Tag</Label>
                <Textarea
                  id="thirdPartyScriptTag"
                  rows={8}
                  className="font-mono text-sm"
                  {...register("thirdPartyScriptTag")}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting} className="min-w-32">
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Tags
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
