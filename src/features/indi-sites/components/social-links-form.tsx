"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Loader2, Save } from "lucide-react";
import { socialLinksSchema, type SocialLinksFormData } from "../lib/validation";
import { showSuccess, showError } from "@/shared/lib/toast";
import type { UserData } from "../types";

interface SocialLinksFormProps {
  user: UserData;
  onSubmit: (data: SocialLinksFormData) => Promise<void>;
}

export function SocialLinksForm({ user, onSubmit }: SocialLinksFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SocialLinksFormData>({
    resolver: zod<PERSON><PERSON>olver(socialLinksSchema),
    defaultValues: {
      instagram: user.instagram || "",
      facebook: user.facebook || "",
      linkedin: user.linkedin || "",
      twitter: user.twitter || "",
      youtube: user.youtube || "",
      applicationLink: user.applicationLink || "",
      appointmentScheduleLink: user.appointmentScheduleLink || "",
    },
  });

  const handleFormSubmit = async (data: SocialLinksFormData) => {
    setIsSubmitting(true);

    try {
      await onSubmit(data);
      showSuccess("Social Links Updated", "Your social links have been updated successfully!");
    } catch (error) {
      console.error("Form submission error:", error);
      showError("Update Failed", error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Social Media & Business Links</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Social Media Links */}
              <div className="space-y-2">
                <Label htmlFor="instagram">Instagram Page</Label>
                <Input
                  id="instagram"
                  placeholder="https://instagram.com/jane-doe"
                  {...register("instagram")}
                />
                {errors.instagram && (
                  <p className="text-sm text-red-500">{errors.instagram.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="facebook">Facebook Page</Label>
                <Input
                  id="facebook"
                  placeholder="https://facebook.com/jane-doe"
                  {...register("facebook")}
                />
                {errors.facebook && (
                  <p className="text-sm text-red-500">{errors.facebook.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="linkedin">LinkedIn Page</Label>
                <Input
                  id="linkedin"
                  placeholder="https://linkedin.com/in/jane-doe"
                  {...register("linkedin")}
                />
                {errors.linkedin && (
                  <p className="text-sm text-red-500">{errors.linkedin.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="twitter">Twitter Page</Label>
                <Input
                  id="twitter"
                  placeholder="https://twitter.com/jane-doe"
                  {...register("twitter")}
                />
                {errors.twitter && (
                  <p className="text-sm text-red-500">{errors.twitter.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="youtube">YouTube Channel</Label>
                <Input
                  id="youtube"
                  placeholder="https://youtube.com/c/jane-doe"
                  {...register("youtube")}
                />
                {errors.youtube && (
                  <p className="text-sm text-red-500">{errors.youtube.message}</p>
                )}
              </div>

              {/* Business Links */}
              <div className="space-y-2">
                <Label htmlFor="applicationLink">Mortgage Application Link</Label>
                <Input
                  id="applicationLink"
                  placeholder="https://mtgapp.scarlettnetwork.com/broker-name/home"
                  {...register("applicationLink")}
                />
                {errors.applicationLink && (
                  <p className="text-sm text-red-500">{errors.applicationLink.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="appointmentScheduleLink">Appointment Schedule Link (i.e. Calendly)</Label>
                <Input
                  id="appointmentScheduleLink"
                  placeholder="Calendly or Other"
                  {...register("appointmentScheduleLink")}
                />
                {errors.appointmentScheduleLink && (
                  <p className="text-sm text-red-500">{errors.appointmentScheduleLink.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting} className="min-w-32">
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Links
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
