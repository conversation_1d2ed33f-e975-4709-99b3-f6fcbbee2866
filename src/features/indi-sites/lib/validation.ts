import { z } from "zod";

// Phone number validation regex
const phoneRegex = /^[\d\s\-\(\)\+\.]+$/;

// URL validation helper
const urlSchema = z
  .string()
  .optional()
  .refine(
    (val) => {
      if (!val || val.trim() === "") return true;
      try {
        new URL(val.startsWith("http") ? val : `https://${val}`);
        return true;
      } catch {
        return false;
      }
    },
    { message: "Please enter a valid URL" }
  );

// Website Info Tab Schema
export const websiteInfoSchema = z.object({
  firstname: z
    .string()
    .min(1, "First name is required")
    .max(50, "First name must be less than 50 characters"),
  
  lastname: z
    .string()
    .min(1, "Last name is required")
    .max(50, "Last name must be less than 50 characters"),
  
  phone: z
    .string()
    .min(1, "Phone number is required")
    .regex(phoneRegex, "Please enter a valid phone number"),

  middlename: z
    .string()
    .max(50, "Middle name must be less than 50 characters")
    .optional(),
  
  titles: z
    .string()
    .max(100, "Titles must be less than 100 characters")
    .optional(),
  
  position: z
    .string()
    .max(100, "Position must be less than 100 characters")
    .optional(),
  
  license: z
    .string()
    .max(50, "License must be less than 50 characters")
    .optional(),
  
  workEmail: z
    .string()
    .email("Please enter a valid email address")
    .optional()
    .or(z.literal("")),
});

// Social Media & Business Links Tab Schema
export const socialLinksSchema = z.object({
  instagram: urlSchema,
  facebook: urlSchema,
  linkedin: urlSchema,
  twitter: urlSchema,
  youtube: urlSchema,
  applicationLink: urlSchema,
  appointmentScheduleLink: urlSchema,
});

// HTML Tags Tab Schema
export const htmlTagsSchema = z.object({
  chatWidgetCode: z.string().optional(),
  reviewWidgetCode: z.string().optional(),
  googleTag: z.string().optional(),
  googleTagManagerInHead: z.string().optional(),
  googleTagManagerInBody: z.string().optional(),
  googleWebsiteVerification: z.string().optional(),
  facebookPixelTag: z.string().optional(),
  thirdPartyScriptTag: z.string().optional(),
});

// Combined schema for the full form
export const indiSitesFormSchema = websiteInfoSchema.merge(socialLinksSchema).merge(htmlTagsSchema);

// Export types
export type WebsiteInfoFormData = z.infer<typeof websiteInfoSchema>;
export type SocialLinksFormData = z.infer<typeof socialLinksSchema>;
export type HtmlTagsFormData = z.infer<typeof htmlTagsSchema>;
export type IndiSitesFormData = z.infer<typeof indiSitesFormSchema>;
