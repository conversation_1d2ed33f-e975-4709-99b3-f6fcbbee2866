/**
 * Format URL to ensure it has a protocol
 */
export function formatUrl(url: string): string {
  if (!url) return "";
  
  // Remove any whitespace
  const cleanUrl = url.trim();
  
  // If it already has a protocol, return as is
  if (cleanUrl.match(/^https?:\/\//)) {
    return cleanUrl;
  }
  
  // Add https:// if no protocol is present
  return `https://${cleanUrl}`;
}

/**
 * Convert SVG element to blob for download/upload
 */
export async function svgToBlob(svgElement: SVGElement): Promise<Blob> {
  // Clone the SVG element to avoid modifying the original
  const clonedSvg = svgElement.cloneNode(true) as SVGElement;
  
  // Find and convert all image elements to base64
  const imageElements = clonedSvg.querySelectorAll('image');
  const imagePromises = Array.from(imageElements).map(async (imgElement) => {
    const href = imgElement.getAttribute('href') || imgElement.getAttribute('xlink:href');
    if (href && !href.startsWith('data:')) {
      try {
        // Fetch the image and convert to base64
        const response = await fetch(href);
        const blob = await response.blob();
        const base64 = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });
        
        // Update the image href to use base64
        imgElement.setAttribute('href', base64);
        if (imgElement.hasAttribute('xlink:href')) {
          imgElement.setAttribute('xlink:href', base64);
        }
      } catch (error) {
        console.warn('Failed to convert image to base64:', href, error);
        // Keep the original href if conversion fails
      }
    }
  });
  
  // Wait for all images to be converted
  await Promise.all(imagePromises);
  
  // Now serialize the modified SVG
  const svgData = new XMLSerializer().serializeToString(clonedSvg);
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  const img = new Image();
  
  return new Promise((resolve, reject) => {
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx?.drawImage(img, 0, 0);
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error("Failed to convert SVG to blob"));
        }
      }, "image/png");
    };
    
    img.onerror = () => reject(new Error("Failed to load SVG"));
    img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
  });
}
