"use client";

import React, { useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { QRCodeSVG } from "qrcode.react";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Loader2, QrCode, Save } from "lucide-react";
import { qrCodeFormSchema, type QRCodeFormData } from "../lib/validation";
import { formatUrl } from "../lib/utils";
import { useQRCodes } from "../hooks/use-qr-codes";
import type { QRCode } from "../types";

interface QRCodeFormProps {
  initialQRCodes?: QRCode[];
  onQRCodeSaved?: (qrCode: QRCode) => void;
}

export function QRCodeForm({ 
  initialQRCodes = [], 
  onQRCodeSaved
}: QRCodeFormProps) {
  const [showQRCode, setShowQRCode] = useState(false);
  const [generatedUrl, setGeneratedUrl] = useState<string | null>(null);
  const qrCodeRef = useRef<HTMLDivElement>(null);
  
  const { processing, saveQRCode } = useQRCodes(initialQRCodes);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<QRCodeFormData>({
    resolver: zodResolver(qrCodeFormSchema),
    defaultValues: {
      url: "",
    },
  });

  const urlValue = watch("url");

  const handleGenerateQR = (data: QRCodeFormData) => {
    const formattedUrl = formatUrl(data.url);
    setGeneratedUrl(formattedUrl);
    setShowQRCode(true);
  };

  const handleSaveQR = async () => {
    if (!generatedUrl || !qrCodeRef.current) return;

    try {
      const svgElement = qrCodeRef.current.querySelector('svg');
      if (!svgElement) {
        throw new Error('QR code SVG not found');
      }

      const savedQRCode = await saveQRCode(generatedUrl, svgElement);
      
      if (onQRCodeSaved) {
        onQRCodeSaved(savedQRCode);
      }

      // Reset form after successful save
      reset();
      setGeneratedUrl(null);
      setShowQRCode(false);
    } catch (error) {
      console.error('Error saving QR code:', error);
      // Toast notification is handled in the useQRCodes hook
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          Generate QR Code
        </CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <form onSubmit={handleSubmit(handleGenerateQR)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="url">URL</Label>
            <Input
              id="url"
              type="text"
              placeholder="https://example.com"
              {...register("url")}
              className={errors.url ? "border-red-500" : ""}
            />
            {errors.url && (
              <p 
                className="text-sm text-red-500"
                dangerouslySetInnerHTML={{ 
                  __html: (errors.url?.message || '').replace(
                    /<u>(.*?)<\/u>/g, 
                    '<span class="underline">$1</span>'
                  )
                }}
              />
            )}
          </div>

          <Button
            type="submit"
            disabled={processing.isGenerating || !urlValue}
            
          >
            {processing.isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <QrCode className="h-4 w-4 mr-2" />
                Generate QR Code
              </>
            )}
          </Button>
        </form>

        {showQRCode && generatedUrl && (
          <div className="space-y-4 mt-8">
            <div className="flex justify-center">
              <div
                ref={qrCodeRef}
                className="p-4 bg-white rounded-lg border inline-block"
              >
                <QRCodeSVG
                  value={generatedUrl}
                  size={300}
                  bgColor="#FFFFFF"
                  fgColor="#000000"
                  level="H"
                  includeMargin={true}
                  imageSettings={{
                    src: "/images/indi-symbol.png",
                    height: 60,
                    width: 60,
                    excavate: true,
                    crossOrigin: "anonymous"
                  }}
                />
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground break-all">
                {generatedUrl}
              </p>
            </div>
            <div className="flex justify-center">
              <Button
                onClick={handleSaveQR}
                disabled={processing.isSaving}
                className="w-full max-w-xs"
              >
                {processing.isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save QR Code
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
