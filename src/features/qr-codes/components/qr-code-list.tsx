"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { Trash2, Download, Loader2 } from "lucide-react";
import { saveAs } from "file-saver";
import { fetchImageWithProxy } from "@/shared/lib/s3-proxy";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/shared/ui/alert-dialog";
import { useQRCodes } from "../hooks/use-qr-codes";
import type { QRCode } from "../types";

interface QRCodeListProps {
  qrCodes: QRCode[];
  onQRCodeDeleted?: (qrCodeId: string) => void;
}

export function QRCodeList({ 
  qrCodes: initialQRCodes, 
  onQRCodeDeleted
}: QRCodeListProps) {
  const [qrCodes, setQrCodes] = useState<QRCode[]>(initialQRCodes);
  const { processing, deleteQRCode } = useQRCodes(initialQRCodes);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Update local state when props change (when auth context updates)
  useEffect(() => {
    setQrCodes(initialQRCodes);
  }, [initialQRCodes]);

  const handleDelete = async (qrCodeId: string) => {
    setDeletingId(qrCodeId);
    try {
      await deleteQRCode(qrCodeId);
      
      if (onQRCodeDeleted) {
        onQRCodeDeleted(qrCodeId);
      }
    } catch (error) {
      console.error('Error deleting QR code:', error);
      // Toast notification is handled in the useQRCodes hook
    } finally {
      setDeletingId(null);
    }
  };

  const handleDownload = async (qrCode: QRCode) => {
    if (qrCode.qrImage) {
      try {
        // Create a clean filename from the URL
        const filename = `qrcode-${qrCode.url.replace(/[^a-zA-Z0-9]/g, '_')}.png`;
        
        // Use fetchImageWithProxy for proper image handling and CORS support
        const { arrayBuffer } = await fetchImageWithProxy(qrCode.qrImage);
        
        // Create blob with proper MIME type for PNG
        const blob = new Blob([arrayBuffer], { type: 'image/png' });
        saveAs(blob, filename);
      } catch (error) {
        console.error("Download failed:", error);
        // Fallback to opening in new tab if download fails
        window.open(qrCode.qrImage, "_blank");
      }
    }
  };

  if (qrCodes.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">You don't have any QR codes yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Your QR Codes</h3>
      
      {processing.isDeleting && (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Deleting...</span>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {qrCodes.map((qrCode, index) => (
          <Card key={qrCode.id || qrCode.documentId || qrCode.url || `qr-${index}`} className="overflow-hidden">
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* QR Code Image */}
                <div className="flex justify-center">
                  <img
                    src={qrCode.qrImage}
                    alt={`QR Code - ${qrCode.url}`}
                    className="w-32 h-32 object-contain border rounded"
                  />
                </div>

                {/* URL */}
                <div className="text-center">
                  <p className="text-sm text-muted-foreground break-all line-clamp-2">
                    {qrCode.url}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownload(qrCode)}
                    className="flex-1 mr-2"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={deletingId === (qrCode.id || qrCode.documentId || '')}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        {deletingId === (qrCode.id || qrCode.documentId || '') ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete QR Code</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this QR code? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(qrCode.id || qrCode.documentId || '')}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
