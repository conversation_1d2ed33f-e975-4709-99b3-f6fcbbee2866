export interface QRCode {
  id?: string;
  documentId?: string;
  url: string;
  qrImage: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface QRCodeFormData {
  url: string;
}

export interface QRCodeGenerationState {
  url: string | null;
  isVisible: boolean;
  svgVisible: boolean;
}

export interface QRCodeProcessingState {
  isSaving: boolean;
  isDeleting: boolean;
  isGenerating: boolean;
}

export interface User {
  id: string;
  qrCodes?: QRCode[];
  [key: string]: any;
}
