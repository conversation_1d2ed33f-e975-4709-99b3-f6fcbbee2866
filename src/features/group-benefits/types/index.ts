export interface GroupBenefitsFile {
  id: number;
  title: string;
  file: {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    width: number | null;
    height: number | null;
    formats: Record<string, any>;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: Record<string, any> | null;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    locale: string | null;
    publishedAt: string;
    folderPath: string | null;
  };
}

export interface GroupBenefitsPageData {
  id: number;
  pageTitle: string;
  slug: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string | null;
  documentId: string;
  locale: string | null;
  files: GroupBenefitsFile[];
}

export interface GroupBenefitsApiResponse {
  data: GroupBenefitsPageData;
  meta: Record<string, any>;
}
