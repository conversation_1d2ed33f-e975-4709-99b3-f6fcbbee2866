"use client";

import { useState, useEffect } from "react";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getAuthHeaders } from "@/shared/lib/auth";
import type { GroupBenefitsApiResponse } from "@/features/group-benefits/types";

export function useGroupBenefits() {
  const [data, setData] = useState<GroupBenefitsApiResponse["data"] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userAuth } = useAuthContext();

  useEffect(() => {
    async function fetchGroupBenefits() {
      if (!userAuth.isAuth || !userAuth.initialized) {
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/page-group-benefits`, {
          headers: getAuthHeaders(),
          cache: 'no-store',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch group benefits data');
        }

        const result: GroupBenefitsApiResponse = await response.json();
        setData(result.data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        console.error('Error fetching group benefits:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchGroupBenefits();
  }, [userAuth.isAuth, userAuth.initialized]);

  return {
    data,
    isLoading,
    error,
  };
}
