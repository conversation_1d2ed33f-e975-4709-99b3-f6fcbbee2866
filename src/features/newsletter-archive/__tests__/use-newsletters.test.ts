import { renderHook } from '@testing-library/react';
import { useNewsletters } from '../hooks/use-newsletters';
import { apiClient } from '@/shared/lib/api';

// Mock the API client
jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('useNewsletters', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useNewsletters());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.items).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useNewsletters());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useNewsletters());

    expect(() => result.current.retry()).not.toThrow();
  });

  it('should handle API error gracefully', async () => {
    mockApiClient.get.mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useNewsletters());

    expect(result.current.isLoading).toBe(true);
  });

  it('should handle initial state correctly', () => {
    const { result } = renderHook(() => useNewsletters());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.items).toEqual([]);
    expect(result.current.error).toBe(null);
    expect(typeof result.current.retry).toBe('function');
  });
});
