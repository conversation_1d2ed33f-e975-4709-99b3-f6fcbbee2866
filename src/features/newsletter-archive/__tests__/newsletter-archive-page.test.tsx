import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import NewsletterArchivePage from '../components/newsletters-list-page';

// Mock the useNewsletters hook
jest.mock('../hooks/use-newsletters', () => ({
  useNewsletters: jest.fn(),
}));

import { useNewsletters } from '../hooks/use-newsletters';

const mockUseNewsletters = useNewsletters as jest.MockedFunction<typeof useNewsletters>;

const mockData = {
  newsletters: [
    {
      id: '1',
      title: 'Test Newsletter',
      description: 'This is a test newsletter',
      slug: 'test-newsletter',
      publishedAt: '2024-01-01',
      pdfFile: {
        url: '/test.pdf'
      }
    }
  ],
  pagination: {
    page: 1,
    pageSize: 10,
    pageCount: 1,
    total: 1
  }
};

describe('NewsletterArchivePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state correctly', () => {
    mockUseNewsletters.mockReturnValue({
      items: [],
      isLoading: true,
      error: null,
      retry: jest.fn(),
    });

    render(<NewsletterArchivePage />);
    
    const skeletons = document.querySelectorAll('[data-slot="skeleton"]');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('renders error state correctly', () => {
    const mockRetry = jest.fn();
    mockUseNewsletters.mockReturnValue({
      items: [],
      isLoading: false,
      error: 'Failed to load data',
      retry: mockRetry,
    });

    render(<NewsletterArchivePage />);
    
    expect(screen.getByText('Error loading content: Failed to load data')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('renders newsletters correctly when data is loaded', () => {
    mockUseNewsletters.mockReturnValue({
      items: mockData.newsletters,
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<NewsletterArchivePage />);
    
    expect(screen.getByText('Newsletters')).toBeInTheDocument();
  });

  it('renders component without crashing', () => {
    mockUseNewsletters.mockReturnValue({
      items: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    const { container } = render(<NewsletterArchivePage />);
    expect(container).toBeInTheDocument();
  });

  it('handles empty newsletters gracefully', () => {
    mockUseNewsletters.mockReturnValue({
      items: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });
    
    render(<NewsletterArchivePage />);
    
    expect(screen.getByText('Newsletters')).toBeInTheDocument();
  });
});
