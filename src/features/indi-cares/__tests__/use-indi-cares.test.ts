import { renderHook } from '@testing-library/react';
import { useIndiCares } from '../hooks/use-indi-cares';

jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

describe('useIndiCares', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useIndiCares());

    expect(result.current.isLoading).toBe(true);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useIndiCares());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useIndiCares());

    expect(() => result.current.retry()).not.toThrow();
  });
});
