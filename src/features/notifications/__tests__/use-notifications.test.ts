import { renderHook } from '@testing-library/react';
import { useNotifications } from '../hooks/use-notifications';
import { apiClient } from '@/shared/lib/api';

// Mock the API client
jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('useNotifications', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useNotifications({
      user: { id: 'user-123', name: 'Test User' }
    }));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.notifications).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useNotifications({
      user: { id: 'user-123', name: 'Test User' }
    }));

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useNotifications({
      user: { id: 'user-123', name: 'Test User' }
    }));

    expect(() => result.current.retry()).not.toThrow();
  });

  it('should handle API error gracefully', async () => {
    mockApiClient.get.mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useNotifications({
      user: { id: 'user-123', name: 'Test User' }
    }));

    expect(result.current.isLoading).toBe(true);
  });

  it('should handle missing user', () => {
    const { result } = renderHook(() => useNotifications());

    expect(result.current.isLoading).toBe(true);
  });
});
