import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import NotificationsListPage from '../components/notifications-list-page';

// Mock the useNotifications hook
jest.mock('../hooks/use-notifications', () => ({
  useNotifications: jest.fn(),
}));

import { useNotifications } from '../hooks/use-notifications';

const mockUseNotifications = useNotifications as jest.MockedFunction<typeof useNotifications>;

// Mock the components
jest.mock('@/shared/components', () => ({
  MarkdownRenderer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="markdown-renderer">{children}</div>
  ),
}));

const mockUser = {
  id: 'user-123',
  name: 'Test User',
  email: '<EMAIL>',
};

describe('NotificationsListPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state correctly', () => {
    mockUseNotifications.mockReturnValue({
      notifications: [],
      isLoading: true,
      error: null,
      retry: jest.fn(),
    });

    render(<NotificationsListPage user={mockUser} />);
    
    // Should show loading skeletons
    const skeletons = document.querySelectorAll('[data-slot="skeleton"]');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('renders error state correctly', () => {
    const mockRetry = jest.fn();
    mockUseNotifications.mockReturnValue({
      notifications: [],
      isLoading: false,
      error: 'Failed to load data',
      retry: mockRetry,
    });

    render(<NotificationsListPage user={mockUser} />);

    expect(screen.getByText('Error loading notifications: Failed to load data')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('renders notifications correctly when data is loaded', () => {
    const mockNotifications = [
      {
        id: '1',
        title: 'Test Notification',
        content: 'This is a test notification',
        showToAll: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
        publishedAt: '2024-01-01',
        teams: [],
        users: []
      }
    ];

    mockUseNotifications.mockReturnValue({
      notifications: mockNotifications,
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<NotificationsListPage user={mockUser} />);

    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search notifications...')).toBeInTheDocument();
    expect(screen.getByText('Test Notification')).toBeInTheDocument();
  });

  it('renders component without crashing', () => {
    mockUseNotifications.mockReturnValue({
      notifications: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    const { container } = render(<NotificationsListPage user={mockUser} />);
    expect(container).toBeInTheDocument();
  });

  it('handles empty notifications gracefully', () => {
    mockUseNotifications.mockReturnValue({
      notifications: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });
    
    render(<NotificationsListPage user={mockUser} />);
    
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('There are no new notifications.')).toBeInTheDocument();
  });

  it('handles missing user gracefully', () => {
    mockUseNotifications.mockReturnValue({
      notifications: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<NotificationsListPage />);

    expect(screen.getByText('Notifications')).toBeInTheDocument();
  });
});
