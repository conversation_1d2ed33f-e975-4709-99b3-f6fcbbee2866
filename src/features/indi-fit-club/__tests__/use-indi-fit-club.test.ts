import { renderHook } from '@testing-library/react';
import { useIndiFitClub } from '../hooks/use-indi-fit-club';

jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

describe('useIndiFitClub', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useIndiFitClub());

    expect(result.current.isLoading).toBe(true);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useIndiFitClub());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useIndiFitClub());

    expect(() => result.current.retry()).not.toThrow();
  });
});
