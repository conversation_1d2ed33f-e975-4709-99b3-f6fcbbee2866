import { renderHook } from '@testing-library/react';
import { useResources } from '../hooks/use-resources';

jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

describe('useResources', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useResources());

    expect(result.current.isLoading).toBe(true);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useResources());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useResources());

    expect(() => result.current.retry()).not.toThrow();
  });
});
