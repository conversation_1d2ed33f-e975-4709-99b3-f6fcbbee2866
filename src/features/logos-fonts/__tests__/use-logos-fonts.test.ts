import { renderHook } from '@testing-library/react';
import { useLogosFonts } from '../hooks/use-logos-fonts';

jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

describe('useLogosFonts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useLogosFonts());

    expect(result.current.isLoading).toBe(true);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useLogosFonts());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useLogosFonts());

    expect(() => result.current.retry()).not.toThrow();
  });
});
