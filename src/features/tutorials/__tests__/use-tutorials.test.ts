import { renderHook } from '@testing-library/react';
import { useTutorials } from '../hooks/use-tutorials';

jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

describe('useTutorials', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useTutorials());

    expect(result.current.isLoading).toBe(true);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useTutorials());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useTutorials());

    expect(() => result.current.retry()).not.toThrow();
  });
});
