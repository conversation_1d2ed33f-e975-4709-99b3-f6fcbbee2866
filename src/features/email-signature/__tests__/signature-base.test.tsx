import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useColors, useIconPicker, useCopyHtml, useHtmlModal } from '../components/signature-base';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(),
  },
});

// Mock document.execCommand
document.execCommand = jest.fn();

// Test component for useColors
const ColorsTestComponent = ({ customStyle }: { customStyle?: Record<string, unknown> }) => {
  const colors = useColors(customStyle);
  return (
    <div data-testid="colors">
      {colors ? `${colors.primary}-${colors.secondary}` : 'null'}
    </div>
  );
};

// Test component for useCopyHtml
const CopyTestComponent = () => {
  const ref = React.useRef<HTMLDivElement>(null);
  const { copy } = useCopyHtml(ref);

  return (
    <div>
      <div ref={ref} data-testid="content">Test content</div>
      <button onClick={copy} data-testid="copy-button">Copy</button>
    </div>
  );
};

// Test component for useHtmlModal
const HtmlModalTestComponent = () => {
  const ref = React.useRef<HTMLDivElement>(null);
  const { show, html, open, confirm } = useHtmlModal(ref);

  return (
    <div>
      <div ref={ref} data-testid="content"><p>Test HTML</p></div>
      <button onClick={open} data-testid="open-button">Open</button>
      <button onClick={() => confirm(true)} data-testid="confirm-button">Confirm</button>
      <div data-testid="show">{show ? 'true' : 'false'}</div>
      <div data-testid="html">{html}</div>
    </div>
  );
};

describe('signature-base utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useColors', () => {
    it('returns null when no custom style', () => {
      render(<ColorsTestComponent />);
      expect(screen.getByTestId('colors')).toHaveTextContent('null');
    });

    it('returns null when style not enabled', () => {
      const customStyle = { styleEnabled: false, styles: { highlightColorHex: '#ff0000' } };
      render(<ColorsTestComponent customStyle={customStyle} />);
      expect(screen.getByTestId('colors')).toHaveTextContent('null');
    });

    it('returns colors when style enabled', () => {
      const customStyle = {
        styleEnabled: true,
        styles: {
          highlightColorHex: '#ff0000',
          darkColorHex: '#000000',
        },
      };
      render(<ColorsTestComponent customStyle={customStyle} />);
      expect(screen.getByTestId('colors')).toHaveTextContent('#ff0000-#000000');
    });
  });

  describe('useIconPicker', () => {
    it('returns a function that picks icons based on style', () => {
      const customStyle = { styleEnabled: true };
      const iconPicker = useIconPicker(customStyle, false);
      expect(typeof iconPicker).toBe('function');
    });

    it('returns a function even when no custom style', () => {
      const iconPicker = useIconPicker(null, false);
      expect(typeof iconPicker).toBe('function');
    });
  });

  describe('useCopyHtml', () => {
    it('copies content to clipboard', () => {
      // Mock window.getSelection
      const mockSelection = {
        removeAllRanges: jest.fn(),
        addRange: jest.fn(),
      };
      Object.defineProperty(window, 'getSelection', {
        value: () => mockSelection,
      });

      // Mock document.createRange
      const mockRange = {
        selectNode: jest.fn(),
        commonAncestorContainer: document.createElement('div'),
        cloneContents: jest.fn(),
        cloneRange: jest.fn(),
        collapse: jest.fn(),
        compareBoundaryPoints: jest.fn(),
        comparePoint: jest.fn(),
        createContextualFragment: jest.fn(),
        deleteContents: jest.fn(),
        detach: jest.fn(),
        extractContents: jest.fn(),
        getBoundingClientRect: jest.fn(),
        getClientRects: jest.fn(),
        insertNode: jest.fn(),
        intersectsNode: jest.fn(),
        isPointInRange: jest.fn(),
        selectNodeContents: jest.fn(),
        setEnd: jest.fn(),
        setEndAfter: jest.fn(),
        setEndBefore: jest.fn(),
        setStart: jest.fn(),
        setStartAfter: jest.fn(),
        setStartBefore: jest.fn(),
        surroundContents: jest.fn(),
        toString: jest.fn(),
        startContainer: document.createElement('div'),
        startOffset: 0,
        endContainer: document.createElement('div'),
        endOffset: 0,
        collapsed: false,
      } as unknown as Range;
      
      document.createRange = jest.fn(() => mockRange);

      render(<CopyTestComponent />);
      
      const copyButton = screen.getByTestId('copy-button');
      fireEvent.click(copyButton);

      expect(mockRange.selectNode).toHaveBeenCalled();
      expect(mockSelection.removeAllRanges).toHaveBeenCalled();
      expect(mockSelection.addRange).toHaveBeenCalled();
      expect(document.execCommand).toHaveBeenCalledWith('copy');
    });
  });

  describe('useHtmlModal', () => {
    it('opens modal and shows HTML', () => {
      render(<HtmlModalTestComponent />);
      
      const openButton = screen.getByTestId('open-button');
      fireEvent.click(openButton);

      expect(screen.getByTestId('show')).toHaveTextContent('true');
      expect(screen.getByTestId('html')).toHaveTextContent('<p>Test HTML</p>');
    });

    it('confirms and copies HTML to clipboard', async () => {
      render(<HtmlModalTestComponent />);
      
      const openButton = screen.getByTestId('open-button');
      fireEvent.click(openButton);

      const confirmButton = screen.getByTestId('confirm-button');
      fireEvent.click(confirmButton);

      expect(navigator.clipboard.writeText).toHaveBeenCalled();
      expect(screen.getByTestId('show')).toHaveTextContent('false');
    });
  });
});
