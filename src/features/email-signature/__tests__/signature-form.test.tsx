import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SignatureForm } from '../components/signature-form';
import { SignatureProvider } from '../context/signature-context';

// Mock the format-url function
jest.mock('@/shared/lib/format-url', () => ({
  __esModule: true,
  default: jest.fn((url: string) => url.startsWith('http') ? url : `https://${url}`),
}));

// Mock the auth hook
jest.mock('@/shared/lib/auth', () => ({
  getCookie: jest.fn((name: string) => {
    if (name === 'jwt') return 'mock-jwt-token';
    if (name === 'userId') return 'mock-user-id';
    return null;
  }),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

const mockUser = {
  id: '1',
  name: '<PERSON>',
  firstname: '<PERSON>',
  lastname: '<PERSON><PERSON>',
  email: '<EMAIL>',
  role: 'user',
  position: 'Real Estate Agent',
  phone: '************',
  website: 'example.com',
  team: {
    tagline: 'Your trusted real estate team',
  },
};

const renderWithProvider = (component: React.ReactElement) => {
  const initialContext = {
    firstname: '',
    lastname: '',
    email: '',
    position: '',
    phone: '',
    website: '',
    team: { logo: { url: '' } },
  };

  return render(
    <SignatureProvider initial={initialContext}>
      {component}
    </SignatureProvider>
  );
};

describe('SignatureForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ circularPhotoUrl: 'https://example.com/photo.jpg' }),
    });
  });

  it('renders form fields with user data', () => {
    renderWithProvider(<SignatureForm user={mockUser} />);

    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Real Estate Agent')).toBeInTheDocument();
    expect(screen.getByDisplayValue('************')).toBeInTheDocument();
    expect(screen.getByDisplayValue('example.com')).toBeInTheDocument();
  });

  it('formats URLs on input change', async () => {
    renderWithProvider(<SignatureForm user={mockUser} />);

    const websiteInput = screen.getByDisplayValue('example.com');
    fireEvent.change(websiteInput, { target: { value: 'newsite.com' } });

    await waitFor(() => {
      expect(websiteInput).toHaveValue('https://newsite.com');
    });
  });

  it('handles phone number formatting', async () => {
    renderWithProvider(<SignatureForm user={mockUser} />);

    const phoneInput = screen.getByDisplayValue('************');
    fireEvent.change(phoneInput, { target: { value: '************' } });

    // The phone formatting logic should handle the input
    expect(phoneInput).toBeInTheDocument();
  });

  it('renders component without crashing', () => {
    const { container } = renderWithProvider(<SignatureForm user={mockUser} />);
    expect(container).toBeInTheDocument();
  });

  it('handles missing data gracefully', () => {
    const emptyData = {
      id: '1',
      name: 'John Doe',
      firstname: '',
      lastname: 'Doe',
      email: '',
      role: 'user',
      position: 'Real Estate Agent',
      phone: '************',
      website: 'example.com',
      team: {
        tagline: 'Your trusted real estate team',
      },
    };
    
    renderWithProvider(<SignatureForm user={emptyData} />);
    
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Real Estate Agent')).toBeInTheDocument();
    expect(screen.getByDisplayValue('************')).toBeInTheDocument();
    expect(screen.getByDisplayValue('example.com')).toBeInTheDocument();
  });
});
