export interface TechnologyLogo {
  id: number;
  name: string;
  alternativeText: string;
  caption: string;
  width: number;
  height: number;
  formats: {
    thumbnail?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      provider_metadata?: {
        public_id: string;
        resource_type: string;
      };
    };
    small?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      provider_metadata?: {
        public_id: string;
        resource_type: string;
      };
    };
    medium?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      provider_metadata?: {
        public_id: string;
        resource_type: string;
      };
    };
    large?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
      provider_metadata?: {
        public_id: string;
        resource_type: string;
      };
    };
    squared?: {
      ext: string;
      url: string;
      hash: string;
      mime: string;
      name: string;
      path: string | null;
      size: number;
      width: number;
      height: number;
    };
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
  documentId: string;
  publishedAt: string;
}

export interface Technology {
  id: number;
  title: string;
  link: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  documentId: string;
  logo: TechnologyLogo;
}

export interface TechnologiesApiResponse {
  data: Technology[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}
