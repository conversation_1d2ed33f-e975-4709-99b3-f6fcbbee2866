"use client";

import { useState, useEffect } from "react";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getAuthHeaders } from "@/shared/lib/auth";
import type { TechnologiesApiResponse } from "@/features/technology/types";

export function useTechnology() {
  const [data, setData] = useState<TechnologiesApiResponse["data"] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { userAuth } = useAuthContext();

  useEffect(() => {
    async function fetchTechnologies() {
      if (!userAuth.isAuth || !userAuth.initialized) {
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/technologies?populate=*`, {
          headers: getAuthHeaders(),
          cache: 'no-store',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch technologies data');
        }

        const result: TechnologiesApiResponse = await response.json();
        setData(result.data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        console.error('Error fetching technologies:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchTechnologies();
  }, [userAuth.isAuth, userAuth.initialized]);

  return {
    data,
    isLoading,
    error,
  };
}
