"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { ExternalLink } from "lucide-react";
import type { Technology } from "../types";

interface TechnologyCardProps {
  technology: Technology;
}

export function TechnologyCard({ technology }: TechnologyCardProps) {
  const formatUrl = (url: string): string => {
    if (!url) return "";
    
    // Remove any whitespace
    const cleanUrl = url.trim();
    
    // If it already has a protocol, return as is
    if (cleanUrl.match(/^https?:\/\//)) {
      return cleanUrl;
    }
    
    // Add https:// if no protocol is present
    return `https://${cleanUrl}`;
  };

  const handleGoTo = () => {
    const formattedUrl = formatUrl(technology.link);
    window.open(formattedUrl, '_blank', 'noopener,noreferrer');
  };

  const getLogoUrl = () => {
    // Prefer smaller formats for better performance
    if (technology.logo.formats?.small?.url) {
      return technology.logo.formats.small.url;
    }
    if (technology.logo.formats?.medium?.url) {
      return technology.logo.formats.medium.url;
    }
    if (technology.logo.formats?.thumbnail?.url) {
      return technology.logo.formats.thumbnail.url;
    }
    return technology.logo.url;
  };

  return (
    <Card className="h-full hover:shadow-md transition-shadow">
      <CardContent className="flex flex-col items-center text-center space-y-4 h-full">
        {/* Logo */}
        <div className="flex-shrink-0 w-full flex justify-center">
          <div className="w-full h-auto px-8 py-2 flex items-center justify-center">
            <img
              src={getLogoUrl()}
              alt={technology.logo.alternativeText || technology.title}
              className="max-w-full max-h-full object-contain"
              onError={(e) => {
                // Fallback to original URL if formatted URL fails
                const target = e.target as HTMLImageElement;
                if (target.src !== technology.logo.url) {
                  target.src = technology.logo.url;
                }
              }}
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col justify-between space-y-4">
          <div>
            <h3 className="font-semibold text-lg mb-2">{technology.title}</h3>
            {technology.description && (
              <p className="text-sm text-muted-foreground line-clamp-3">
                {technology.description}
              </p>
            )}
          </div>          
        </div>
      </CardContent>
      <CardFooter>
        {/* Action Button */}
          <Button
            onClick={handleGoTo}
            className="w-full"
            variant="default"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Go To
          </Button>
      </CardFooter>
    </Card>
  );
}
