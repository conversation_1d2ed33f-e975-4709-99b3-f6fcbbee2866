import { renderHook, waitFor } from '@testing-library/react';
import { useAOD } from '../hooks/use-aod';
import { apiClient } from '@/shared/lib/api';

// Mock the API client
jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

const mockApiResponse = {
  data: {
    data: [
      {
        id: 1,
        pageTitle: 'Agent of the Day',
        slug: 'aod',
        pageContent: 'Celebrate our top performing agents',
        topBanner: {
          url: '/images/aod-banner.jpg',
          alternativeText: 'AOD Banner'
        },
        pageThumbnail: {
          url: '/images/aod-thumb.jpg',
          alternativeText: 'AOD Thumbnail'
        },
        features: [
          {
            id: 1,
            title: '<PERSON>',
            description: 'Top performer this month',
            image: {
              url: '/images/john-doe.jpg',
              alternativeText: '<PERSON> photo'
            },
            downloadItem: {
              url: '/files/john-doe.pdf',
              ext: 'pdf'
            }
          },
          {
            id: 2,
            title: '<PERSON>',
            description: 'Excellent customer service',
            image: {
              url: '/images/jane-smith.jpg',
              alternativeText: '<PERSON> photo'
            }
          }
        ]
      }
    ]
  },
  status: 200,
  success: true
};

describe('useAOD', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set up environment variable
    process.env.NEXT_PUBLIC_API_URL = 'http://localhost:1339';
  });

  it('should fetch AOD data successfully', async () => {
    mockApiClient.get.mockResolvedValue(mockApiResponse);

    const { result } = renderHook(() => useAOD());

    // Initially loading
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(null);

    // Wait for data to load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual({
      id: '1',
      title: 'Agent of the Day',
      pageTitle: 'Agent of the Day',
      pageContent: 'Celebrate our top performing agents',
      topBanner: {
        url: '/images/aod-banner.jpg',
        alt: 'AOD Banner'
      },
      pageThumbnail: {
        url: '/images/aod-thumb.jpg',
        alt: 'AOD Thumbnail'
      },
      features: [
        {
          id: '1',
          title: 'John Doe',
          description: 'Top performer this month',
          image: {
            url: 'http://localhost:1339/images/john-doe.jpg',
            alt: 'John Doe photo'
          },
          downloadItem: {
            url: 'http://localhost:1339/files/john-doe.pdf',
            ext: 'pdf'
          }
        },
        {
          id: '2',
          title: 'Jane Smith',
          description: 'Excellent customer service',
          image: {
            url: 'http://localhost:1339/images/jane-smith.jpg',
            alt: 'Jane Smith photo'
          }
        }
      ]
    });
    expect(result.current.error).toBe(null);
  });

  it('should handle API error', async () => {
    const errorMessage = 'Network error';
    mockApiClient.get.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useAOD());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(errorMessage);
  });

  it('should handle empty response', async () => {
    mockApiClient.get.mockResolvedValue({ data: { data: [] }, status: 200, success: true });

    const { result } = renderHook(() => useAOD());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe('AOD page not found');
  });

  it('should handle malformed response', async () => {
    mockApiClient.get.mockResolvedValue({ data: null, status: 200, success: true });

    const { result } = renderHook(() => useAOD());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe('AOD page not found');
  });

  it('should retry on error', async () => {
    mockApiClient.get
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce(mockApiResponse);

    const { result } = renderHook(() => useAOD());

    // Wait for initial error
    await waitFor(() => {
      expect(result.current.error).toBe('Network error');
    });

    // Retry
    result.current.retry();

    // Wait for successful retry
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.data).not.toBe(null);
    });
  });

  it('should handle URLs that already include domain', async () => {
    const responseWithFullUrls = {
      data: {
        data: [
          {
            ...mockApiResponse.data.data[0],
            features: [
              {
                id: 1,
                title: 'John Doe',
                description: 'Top performer',
                image: {
                  url: 'https://example.com/images/john-doe.jpg',
                  alternativeText: 'John Doe photo'
                },
                downloadItem: {
                  url: 'https://example.com/files/john-doe.pdf',
                  ext: 'pdf'
                }
              }
            ]
          }
        ]
      },
      status: 200,
      success: true
    };

    mockApiClient.get.mockResolvedValue(responseWithFullUrls);

    const { result } = renderHook(() => useAOD());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data?.features?.[0]?.image?.url).toBe('https://example.com/images/john-doe.jpg');
    expect(result.current.data?.features?.[0]?.downloadItem?.url).toBe('https://example.com/files/john-doe.pdf');
  });

  it('should handle missing optional fields', async () => {
    const minimalResponse = {
      data: {
        data: [
          {
            id: 1,
            pageTitle: 'Agent of the Day',
            slug: 'aod',
            pageContent: 'Celebrate our top performing agents'
          }
        ]
      },
      status: 200,
      success: true
    };

    mockApiClient.get.mockResolvedValue(minimalResponse);

    const { result } = renderHook(() => useAOD());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.data).toEqual({
      id: '1',
      title: 'Agent of the Day',
      pageTitle: 'Agent of the Day',
      pageContent: 'Celebrate our top performing agents',
      topBanner: undefined,
      pageThumbnail: undefined,
      features: []
    });
  });
});
